﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Context;
using Teletalk.DAL.Infrastructure.Repos.AgentSubmissonRepo;
using Teletalk.DAL.Infrastructure.Repos.AttendanceRepo;
using Teletalk.DAL.Infrastructure.Repos.CampaignRepository;
using Teletalk.DAL.Infrastructure.Repos.FormDatatypeRepository;
using Teletalk.DAL.Infrastructure.Repos.InterviewSubmissionRepository;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.LaptopsRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.MediaRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.NetworkDevicesRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.ServersRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.SoftwareRepo;
using Teletalk.DAL.Infrastructure.Repos.PaySlipRepository;
using Teletalk.DAL.Infrastructure.Repos.PermissionRepository;
using Teletalk.DAL.Infrastructure.Repos.PermissionRoleRepository;
using Teletalk.DAL.Infrastructure.Repos.QualitySeetRepos;
using Teletalk.DAL.Infrastructure.Repos.TeamLeaderSubmissionRepository;
using Teletalk.DAL.Infrastructure.Repos.TeamNumberCampaignRepository;

namespace Teletalk.DAL.Infrastructure.RepositoryManager
{
    public class RepositoryManager : IRepositoryManager
    {
        private readonly ApplicationDbContext _context;

        private readonly Lazy<IAgentSubmissionRepository> _AgentSubmissionRepository;
        private readonly Lazy<IPermissionRoleRepository> _permissionRoleRepository;
        private readonly Lazy<IPermissionRepository> _permissionRepository;
        private readonly Lazy<ICampaignRepository> _campaignRepository;
        private readonly Lazy<IFormDatatypesRepository> _formDatatypesRepository;
        private readonly Lazy<ILaptopsRepo> _laptopsRepo;
        private readonly Lazy<IMediaRepo> _mediaRepo;
        private readonly Lazy<IServersRepo> _serversRepo;
        private readonly Lazy<ISoftwareRepo> _softwareRepo;
        private readonly Lazy<INetworkDevicesRepo> _networkDevicesRepo;
        private readonly Lazy<IAttendanceRepository> _attendanceRepository;
        private readonly Lazy<IInterviewSubmissionRepository> _interviewSubmissionRepository;
        private readonly Lazy<ITeamNumberCampaignRepo> _iTeamNumberCampaignRepo;
        private readonly Lazy<ITeamLeaderSubmissionRepo> _teamLeaderSubmissionRepo; 
        private readonly Lazy<IQualitySheetRepo> _qualitySheetRepo;
        private readonly Lazy<IPaySlipRepository> _paySlipRepository;
        public RepositoryManager(ApplicationDbContext context)
        {
            _context = context;

            _AgentSubmissionRepository = new Lazy<IAgentSubmissionRepository>(() => new AgentSubmissionRepository(context));
            _permissionRoleRepository = new Lazy<IPermissionRoleRepository>(()=>new PermissionRoleRepository(context));
            _permissionRepository = new Lazy<IPermissionRepository>(()=>new PermissionRepository(context));
            _campaignRepository = new Lazy<ICampaignRepository>(()=>new CampaignRepository(context));
            _formDatatypesRepository = new Lazy<IFormDatatypesRepository>(() => new FormDataTypesRepository(context));
            _laptopsRepo = new Lazy<ILaptopsRepo>(() => new LaptopsRepo(context));
            _mediaRepo = new Lazy<IMediaRepo>(() => new MediaRepo(context));
            _serversRepo = new Lazy<IServersRepo>(() => new ServersRepo(context));
            _softwareRepo = new Lazy<ISoftwareRepo>(() => new SoftwareRepo(context));
            _networkDevicesRepo = new Lazy<INetworkDevicesRepo>(() => new NetworkDevicesRepo(context));
            _interviewSubmissionRepository = new Lazy<IInterviewSubmissionRepository>(() => new InterviewSubmissionRepository(context));
            _attendanceRepository = new Lazy<IAttendanceRepository>(() => new AttendanceRepository(context));
            _iTeamNumberCampaignRepo = new Lazy<ITeamNumberCampaignRepo>(() => new TeamNumberCampaignRepo(context));
            _teamLeaderSubmissionRepo = new Lazy<ITeamLeaderSubmissionRepo>(() => new TeamLeaderSubmissionRepo(context));
            _qualitySheetRepo = new Lazy<IQualitySheetRepo>(() => new QualitySheetRepo(context));
            _paySlipRepository = new Lazy<IPaySlipRepository>(() => new PaySlipRepository(context));
        }
        public IAgentSubmissionRepository AgentSubmission => _AgentSubmissionRepository.Value;
        public IPermissionRoleRepository permissionRoleRepository => _permissionRoleRepository.Value;
        public IPermissionRepository permissionRepository => _permissionRepository.Value;
        public ICampaignRepository CampaignRepository => _campaignRepository.Value;
        public IFormDatatypesRepository FormDatatypeRepository=> _formDatatypesRepository.Value;
        public ILaptopsRepo LaptopsRepo => _laptopsRepo.Value;
        public IMediaRepo MediaRepo => _mediaRepo.Value;
        public IServersRepo ServersRepo => _serversRepo.Value;
        public ISoftwareRepo SoftwareRepo => _softwareRepo.Value;
        public INetworkDevicesRepo NetworkDevicesRepo => _networkDevicesRepo.Value;
        public IInterviewSubmissionRepository InterviewSubmissionRepository => _interviewSubmissionRepository.Value;    

        public IAttendanceRepository AttendanceRepository => _attendanceRepository.Value;
        public ITeamNumberCampaignRepo TeamNumberCampaignRepo => _iTeamNumberCampaignRepo.Value;
        public ITeamLeaderSubmissionRepo TeamLeaderSubmissionRepo => _teamLeaderSubmissionRepo.Value;
        public IQualitySheetRepo QualitySheetRepo => _qualitySheetRepo.Value;
        public IPaySlipRepository PaySlipRepository => _paySlipRepository.Value;
        public async Task SaveAsync() => await _context.SaveChangesAsync();
    }
}
