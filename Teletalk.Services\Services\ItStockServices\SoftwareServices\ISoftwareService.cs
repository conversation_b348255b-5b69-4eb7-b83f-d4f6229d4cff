﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.NetworkDevicesModels;
using Teletalk.Services.Models.ItStockModels.SoftwareModels;

namespace Teletalk.Services.Services.ItStockServices.SoftwareServices
{
    public interface ISoftwareService
    {
        Task<MiddlewareObject<List<SoftwareModel>>> GetAllSoftwares();

        Task<MiddlewareObject<bool>> AddSoftware(AddSoftwareRequest request);

        Task<MiddlewareObject<bool>> DeleteSoftware(Guid id);

        Task<MiddlewareObject<bool>> UpdateSoftware(Guid id, SoftwareForUpdateModel dto);

        Task<MiddlewareObject<SoftwareModel>> GetSoftwareById(Guid id);
    }
}
