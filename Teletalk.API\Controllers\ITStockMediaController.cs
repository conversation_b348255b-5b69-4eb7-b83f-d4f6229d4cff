﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.MediaModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ITStockMediaController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public ITStockMediaController(IServiceManager serviceManager) => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddMedia([FromBody] AddMediaRequest request)
        {
            var result = await _serviceManager.MediaService.AddMedia(request);
            return result;
        }
        [HttpGet()]
        public async Task<MiddlewareObject<List<MediaModel>>> GetAllMedia()
        {
            var result = await _serviceManager.MediaService.GetAllMedia();
            return result;
        }
        [HttpDelete()]
        public async Task<MiddlewareObject<bool>> DeleteMedia(Guid id)
        {
            var result = await _serviceManager.MediaService.DeleteMedia(id);
            return result;
        }
        [HttpPut()]
        public async Task<MiddlewareObject<bool>> UpdateMedia(Guid id, [FromBody] MediaForUpdateModel dto)
        {
            var result = await _serviceManager.MediaService.UpdateMedia(id, dto);
            return result;
        }
        // Get By Id
        [HttpGet("{id}")]
        public async Task<MiddlewareObject<MediaModel>> GetMediaById(Guid id)
        {
            var result = await _serviceManager.MediaService.GetMediaById(id);
            return result;
        }
    }
}
