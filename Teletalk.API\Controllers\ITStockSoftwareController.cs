﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.SoftwareModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ITStockSoftwareController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public ITStockSoftwareController(IServiceManager serviceManager) => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddSoftware([FromBody] AddSoftwareRequest request)
        {
            var result = await _serviceManager.SoftwareService.AddSoftware(request);
            return result;
        }
        [HttpGet()]
        public async Task<MiddlewareObject<List<SoftwareModel>>> GetAllSoftwares()
        {
            var result = await _serviceManager.SoftwareService.GetAllSoftwares();
            return result;
        }
        [HttpDelete()]
        public async Task<MiddlewareObject<bool>> DeleteSoftware(Guid id)
        {
            var result = await _serviceManager.SoftwareService.DeleteSoftware(id);
            return result;
        }
        [HttpPut()]
        public async Task<MiddlewareObject<bool>> UpdateSoftware(Guid id, [FromBody] SoftwareForUpdateModel dto)
        {
            var result = await _serviceManager.SoftwareService.UpdateSoftware(id, dto);
            return result;
        }
        // Get By Id
        [HttpGet("{id}")]
        public async Task<MiddlewareObject<SoftwareModel>> GetSoftwareById(Guid id)
        {
            var result = await _serviceManager.SoftwareService.GetSoftwareById(id);
            return result;
        }
    }
}
