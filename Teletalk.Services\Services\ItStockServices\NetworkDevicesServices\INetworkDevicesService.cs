﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.NetworkDevicesModels;

namespace Teletalk.Services.Services.ItStockServices.NetworkDevicesServices
{
    public interface INetworkDevicesService
    {
        Task<MiddlewareObject<List<NetworkDevicesModel>>> GetAllNetworkDevices();
        Task<MiddlewareObject<bool>> AddNetworkDevice(AddNetworkDevicesRequest request);
        Task<MiddlewareObject<bool>> DeleteNetworkDevice(Guid id);
        Task<MiddlewareObject<bool>> UpdateNetworkDevice(Guid id, NetworkDevicesForUpdateModel dto);
        Task<MiddlewareObject<NetworkDevicesModel>> GetNetworkDeviceById(Guid id);
    }
}
