﻿using Microsoft.Extensions.DependencyInjection;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Logger;
using Teletalk.Services.Services.Register;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.Services.Extensions
{
    public static class ExtensionServices
    {
        public static void ConfigurationAuthenticationService(this IServiceCollection services)
            => services.AddScoped<IAuthService, AuthService>();

        public static void ConfigurationServiceManager(this IServiceCollection services)
            => services.AddScoped<IServiceManager, ServiceManager>();

        public static void ConfigurationRepositoryManager(this IServiceCollection services)
            => services.AddScoped<IRepositoryManager, RepositoryManager>();

        public static void ConfigurationLoggerService(this IServiceCollection services)
            => services.AddSingleton<ILoggerManager, LoggerManager>();
    }
}
