﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Models.PaginationModel;
using Teletalk.Models.RegisterModel;
using Teletalk.Services.Common;
using Teletalk.Services.Models.AuthDTOS;
using Teletalk.Services.Services.Register;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly IAuthService _authService;
        public UsersController(IAuthService authService)
        {
            _authService = authService;
        }
        [HttpPost()]
        public async Task<MiddlewareObject<string>> AddUser([FromBody] RegisterModel model)
        {
            var result = await _authService.RegisterAsync(model);
            return result;
            //if we want to return just some info(token,expireson and message) from the Auth model or to the result we can use anonymous object as follows:
            //return OK(new { token = result.Token, expiration = result.ExpiresON, message = result.Message });
        }
        
        [HttpGet("{id}")]
        public async Task<MiddlewareObject<UserResponseModel>> GetUserDetails(string id)
        {
            var result = await _authService.GetUserDetails(id);
            return result;
        }
        
        [HttpPost("activation/{id}")]
        public async Task<MiddlewareObject<bool>> ChangeUserActivation(string id)
        {
            var result = await _authService.ChangeUserActivation(id);
            return result;
        }
        
        [HttpDelete("{id}")]
        public async Task<MiddlewareObject<bool>> DeleteUser(string id)
        {
            var result = await _authService.DeleteUserByid(id);
            return result;
        }

        [HttpPost("list")]
        public async Task<MiddlewareObject<List<GetAllUsersResponse>>> GetAllUsers([FromQuery] GetAllUserRequest parms)
        {
            var result = await _authService.GetAllUsers(parms);
            return result;
        }

        [HttpPut("{id}")]
        public async Task<MiddlewareObject<string>> UpdateUser(string id, [FromBody] UpdateUserModel model)
        {
            var result = await _authService.UpdateUser(id, model);
            return result;
        }
    }
}
