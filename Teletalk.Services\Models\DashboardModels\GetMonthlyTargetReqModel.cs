﻿namespace Teletalk.Services.Models.DashboardModels
{
    public class GetMonthlyTargetReqModel
    {
        public Guid? CampaignId { get; set; }
        public int? TeamNumber { get; set; }
        public string AgentId { get; set; }
        public DateTime? FromDate { get; set; } = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        public DateTime? ToDate { get; set; } = new DateTime(DateTime.Now.Year, DateTime.Now.Month,
                                             DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));
    }
}
