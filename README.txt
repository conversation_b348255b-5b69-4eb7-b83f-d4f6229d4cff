28/6/2024

- add "Permission" perporty List<permission> we should take the values of permission from front-end
to add in the table that called "PermissionRole" 
userId => userId 
RoleId => RoleId
List<Permission> => permission

for exam:

if we checked on 4 permission, we should insert 4 rows in this fucking table 

------------------------------------------------------------------------------------------------------------

29/6/2024

Commit for modify the database (Campaign):
- (pending) Modify the Register for inserting campaign
- (pending) Modify the Login for model response 

 ----------------------------------------------------------------------------------------------------------------

 Tasks:

1- Get Campaigns endpoint "get-campiagn" (IsActive == true) 
	it should be return the Campaign entity (CampaignController) lookup For maher

2- Create endpoint "get-teams" it should be return list of number from column TeamNumber (Distinct) by Campaign Id (AuthController)

3- when "add-campiagn" i will draw the submission from with the hand. 
    description: .........


----------------------------------------------------------------------------------------------------------------

24/7/2024 

1- in RegisterModel of "Register" API : 
     --> // we need to add the prop for knowing if it is a (new - edit). if "Id" sends with value that means (edit) if else (new).   >>>>>>>>>>> Done <<<<<<<<<<<
     --> and handle the business the update

2- create a lookup api called (get-all-users-name) as drop-down-list --> may be that becomes as searchable - response: {id, username} >>>>>>>>>>>>>>>Done<<<<<<<<<<<<

3- finally: verfiy the two test cases (new - edit) and execute them for testing the scenior. and thank you <3  <<<<<<<<<<<<<<<<<<<< Done >>>>>>>>>>>>>>>>>>

___________________________________________________________________________________________________________________________________________________________________

26/7/2024 
1 - create an endpoint takes "Guid" of the user to return all of the info about him. (with permission) in (UserResponseModel)  >>>>>>>>>>>>>>done<<<<<<<<<<<<<<<<<
2 - Add IsActive flag Column for Application users.                                                                            >>>>>>>>>>>>>>done<<<<<<<<<<<<<<<<<
3 - Create endponit for Acitvate or Deactivate the user (IsActive) true or false.                                              >>>>>>>>>>>>>>done<<<<<<<<<<<<<<<<<                                    
4 - Modify Login service for Active users.                                                                                     >>>>>>>>>>>>>>done<<<<<<<<<<<<<<<<<
5 - Delete user endpoint (Remove from database).                                                                               >>>>>>>>>>>>>>done<<<<<<<<<<<<<<<<<
_______________________________________________________________________________________________________________________________

.. commmit..