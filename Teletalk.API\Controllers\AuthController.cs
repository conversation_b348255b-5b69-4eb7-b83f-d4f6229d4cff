﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Models.PaginationModel;
using Teletalk.Models.RegisterModel;
using Teletalk.Services.Common;
using Teletalk.Services.Extensions;
using Teletalk.Services.Models.AuthDTOS;
using Teletalk.Services.Models.RegisterModel;
using Teletalk.Services.Services.Register;

namespace Teletalk.API.Controllers
{
    [AllowAnonymous]
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }
        
        [HttpPost("login")]
        public async Task<MiddlewareObject<AuthModel>> Login([FromBody] LoginModel loginModel)
        {
            var result = await _authService.GetTokenLoginAsync(loginModel);
            return result;
        }
        [HttpPost("logout")]
        public async Task<MiddlewareObject<bool>> Logout()
        {
            var result = await _authService.logOut();
            return result;
        }
    }
}

