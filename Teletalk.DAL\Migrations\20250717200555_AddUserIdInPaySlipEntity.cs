﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teletalk.DAL.Migrations
{
    public partial class AddUserIdInPaySlipEntity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "UserId",
                table: "Payslips",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Payslips_UserId",
                table: "Payslips",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Payslips_AspNetUsers_UserId",
                table: "Payslips",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payslips_AspNetUsers_UserId",
                table: "Payslips");

            migrationBuilder.DropIndex(
                name: "IX_Payslips_UserId",
                table: "Payslips");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "Payslips");
        }
    }
}
