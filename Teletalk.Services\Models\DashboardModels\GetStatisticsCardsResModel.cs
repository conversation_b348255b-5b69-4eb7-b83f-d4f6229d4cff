﻿namespace Teletalk.Services.Models.DashboardModels
{
    public class GetStatisticsCardsResModel
    {
        public string AgentName { get; set; }
        public int SubmissionsToday { get; set; }
        public int DailyTarget { get; set; }
        public decimal DailyTargetPercentage { get; set; }
        public int SubmissionsThisMonth { get; set; }
        public int MonthlyTarget { get; set; }
        public decimal MonthlyTargetPercentage { get; set; }
        public int AgentRank { get; set; }
    }

    // New models for the updated dashboard structure
    public class DashboardOverviewModel
    {
        public List<ChartDataPointModel> DailySalesChart { get; set; }
        public List<ChartDataPointModel> MonthlySalesChart { get; set; }
        public DashboardCardsModel Cards { get; set; }
    }

    public class DashboardCardsModel
    {
        public decimal DailyTargetPercentage { get; set; }
        public decimal MonthlyTargetPercentage { get; set; }
        public int DailySalesRank { get; set; }
        public int MonthlySalesRank { get; set; }
    }

    public class GetDashboardOverviewReqModel
    {
        public Guid? CampaignId { get; set; }
        public int? TeamNumber { get; set; }
        public string? AgentId { get; set; }
        public DateTime? DailyDate { get; set; } = DateTime.Today;
        public DateTime? MonthlyFromDate { get; set; } 
        public DateTime? MonthlyToDate { get; set; } 
    }
    public class GetDashboardDailyReqModel
    {
        public Guid? CampaignId { get; set; }
        public int? TeamNumber { get; set; }
        public string? AgentId { get; set; }
        public DateTime? DailyDate { get; set; } = DateTime.Today;
    }
    public class GetDashboardMonthlyReqModel
    {
        public Guid? CampaignId { get; set; }
        public int? TeamNumber { get; set; }
        public string? AgentId { get; set; }
        public DateTime? MonthlyFromDate { get; set; }
        public DateTime? MonthlyToDate { get; set; }
    }
}
