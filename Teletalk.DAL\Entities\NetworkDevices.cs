﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.DAL.Entities
{
    public class NetworkDevices
    {
        public Guid Id { get; set; }
        public string DeviceName { get; set; }
        public string Model { get; set; }
        public string Owner { get; set; }
        public string Custodian { get; set; }
        public string Users { get; set; }
        public string AssetID { get; set; }
        public string SerialNumber { get; set; }
        public string IPAddress { get; set; }
        public string NettedIP { get; set; }
        public string HostName { get; set; }
        public string IOS { get; set; }
        public string Location { get; set; }
        public string ApplicationUse { get; set; }
        public string MaintenanceStatus { get; set; }
        public string ConfidentialityRequirements { get; set; }
        public string IntegrityRequirements { get; set; }
        public string AvailabilityRequirements { get; set; }
    }
}
