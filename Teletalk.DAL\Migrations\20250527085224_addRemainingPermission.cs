﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teletalk.DAL.Migrations
{
    public partial class addRemainingPermission : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Permissions",
                columns: new[] { "Id", "Icon", "Path", "PermissionName" },
                values: new object[] { new Guid("93d2cb2f-1847-45cc-993a-242b0baaca53"), "dashboard", "/hrinterviewdashboard", "Interview Dashboard" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("93d2cb2f-1847-45cc-993a-242b0baaca53"));
        }
    }
}
