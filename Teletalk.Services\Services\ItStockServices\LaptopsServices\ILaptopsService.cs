﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.LaptopsModels;

namespace Teletalk.Services.Services.ItStockServices.LaptopsServices
{
    public interface ILaptopsService
    {
        Task<MiddlewareObject<List<LaptopsModel>>> GetAllLaptops();
        Task<MiddlewareObject<bool>> AddLaptop(AddLaptopRequest request);
        Task<MiddlewareObject<bool>> DeleteLaptop(Guid id);
        Task<MiddlewareObject<bool>> UpdateLaptop(Guid id, LaptopforupdateModel dto);
        Task<MiddlewareObject<LaptopsModel>> GetLaptopById(Guid id);
    }
}
