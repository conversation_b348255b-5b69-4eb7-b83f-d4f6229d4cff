﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Models.PayslipModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PayslipController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public PayslipController(IServiceManager serviceManager) 
            => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<IActionResult> CreatePayslip([FromBody] AddPayslipReq req)
        {
            var result = await _serviceManager.PayslipService.CreatePayslip(req);
            return Ok(result);
        }
        [HttpPost("list")]
        public async Task<IActionResult> GetAllPayslip()
        {
            var result = await _serviceManager.PayslipService.GetAllPayslip();
            return Ok(result);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> GetPayslipById(Guid id)
        {
            var result = await _serviceManager.PayslipService.GetPayslipById(id);
            return Ok(result);
        }
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePayslip(Guid id, [FromBody] UpdatePayslipReq req)
        {
            var result = await _serviceManager.PayslipService.UpdatePayslip(id, req);
            return Ok(result);
        }
    }
}
