﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.MediaModels;

namespace Teletalk.Services.Services.ItStockServices.MediaServices
{
    public class MediaService : IMediaService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public MediaService(IRepositoryManager repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> AddMedia(AddMediaRequest request)
        {
            try
            {
                var newMedia = _mapper.Map<Media>(request);
                _repository.MediaRepo.Create(newMedia);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<MediaModel>>> GetAllMedia()
        {
            try
            {
                var query = await _repository.MediaRepo.FindAll(false).ToListAsync();
                var result = _mapper.Map<List<MediaModel>>(query);
                return new MiddlewareObject<List<MediaModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<MediaModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> DeleteMedia(Guid id)
        {
            try
            {
                var query = await _repository.MediaRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                _repository.MediaRepo.Delete(query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Deleted Successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> UpdateMedia(Guid id, MediaForUpdateModel dto)
        {
            try
            {
                var query = await _repository.MediaRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                var UpdatedMedia = _mapper.Map(dto, query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Updated successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<MediaModel>> GetMediaById(Guid id)
        {
            try
            {
                var query = await _repository.MediaRepo.FindByCondition(x => x.Id.Equals(id), false).FirstOrDefaultAsync();
                var result = _mapper.Map<MediaModel>(query);
                return new MiddlewareObject<MediaModel>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<MediaModel>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
