﻿namespace Teletalk.Services.Models.InterviewModels
{
    public class GetInterviewByIdResModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string NationalId { get; set; }
        public string Mobile { get; set; }
        public string PresentAddress { get; set; }
        public string MaritalStatus { get; set; }
        public string MilitaryStatus { get; set; }
        public DateTime Date { get; set; }
        public DateTime BrithOfDate { get; set; }
        public string ReferredToUs { get; set; }
        public string UniversityName { get; set; }
        public string Major { get; set; }
        public string Minor { get; set; }
        public string Degree { get; set; }
        public string EnglishFluency { get; set; }
        public string PostGrad { get; set; }
        public List<GetByIdWorkExperience> WorkExperience { get; set; } // List<WorkExperience> deserialization - seralization
        public List<GetByIdAnyRecommendation> AnyRecommendation { get; set; } // List<Recommendation> deserialization - seralization
        public int Appearance { get; set; }
        public int Personality { get; set; }
        public int SelfConfidence { get; set; }
        public int InterActionSkills { get; set; }
        public int EnglishLanguage { get; set; }
        public int Reading { get; set; }
        public int SellingSkills { get; set; }
        public int Tone { get; set; }
        public string Comment { get; set; }
        public bool? Status { get; set; }
    }
    public class GetByIdWorkExperience
    {
        public string NameAddressOfEmployer { get; set; }
        public string JobTitle { get; set; }
        public DateTime From { get; set; }
        public DateTime To { get; set; }
        public decimal Salary { get; set; }
        public string ReasonForLeaving { get; set; }
    }
    public class GetByIdAnyRecommendation
    {
        public string Name { get; set; }
        public string Number { get; set; }
        public string Project { get; set; }
        public DateTime From { get; set; }
        public DateTime To { get; set; }
    }
}
