﻿using System.Text.Json.Serialization;

namespace Teletalk.Services.Models.DashboardModels
{
    public class GetPercentageOfAgentRankReqModel
    {
        public Guid? CampaignId { get; set; }
        public int? TeamNumber { get; set; }
        public string AgentId { get; set; }
        [JsonIgnore]
        public DateTime? DailyFromDate { get; set; } = DateTime.Now.Date;
        [JsonIgnore]
        public DateTime? DailyToDate { get; set; } = DateTime.Now;
        [JsonIgnore]
        public DateTime? MonthlyFromDate { get; set; } = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        [JsonIgnore]
        public DateTime? MonthlyToDate { get; set; } = new DateTime(DateTime.Now.Year, DateTime.Now.Month,
                                             DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

    }
}
