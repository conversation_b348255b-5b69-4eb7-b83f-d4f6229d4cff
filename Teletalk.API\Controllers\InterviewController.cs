﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.InterviewModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InterviewController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public InterviewController(IServiceManager serviceManager) 
            => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddInterview(AddInterviewReqModel req)
        {
            var result = await _serviceManager.InterviewService.CreateInterviewSubmission(req);
            return result;
        }
        [HttpPost("list")]
        public async Task<MiddlewareObject<List<InterviewDashboardModel>>> GetInterview([FromQuery] GetInterviewPagination req)
        {
            var result = await _serviceManager.InterviewService.GetInterviewDashboard(req);
            return result;
        }
        [HttpPut()]
        public async Task<MiddlewareObject<bool>> UpdateInterview(int id, [FromBody] UpdateInterviewReq req)
        {
            var result = await _serviceManager.InterviewService.UpdateInterviewSubmission(id, req);
            return result;
        }
        [HttpGet]
        public async Task<MiddlewareObject<GetInterviewByIdResModel>> GetInterviewById(int id)
        {
            var result = await _serviceManager.InterviewService.GetInterviewById(id);
            return result;
        }
    }
}
