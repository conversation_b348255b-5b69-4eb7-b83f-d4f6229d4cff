﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Entities;
using Teletalk.Services.Models.AuthDTOS;

namespace Teletalk.Services.Models.RegisterModel
{
    public class AuthModel
    {
        public string Message { get; set; } = "SUCCESS";
        public bool IsAuthenticated { get; set; }
        public string UserName { get; set; }
        public string Role { get; set; }
        public string Token { get; set; }
        public DateTime ExpiresON { get; set; }

        public List<PermissionsModel> Permissions { get; set; }
        public string startingLogging { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }
}
