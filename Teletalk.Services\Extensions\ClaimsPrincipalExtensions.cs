﻿using System.Security.Claims;

namespace Teletalk.Services.Extensions;

public static class ClaimsPrincipalExtensions
{
    public static string? GetUserRole(this ClaimsPrincipal user)
    {
        return user?.Claims
            .FirstOrDefault(c => c.Type == "role" || c.Type == ClaimTypes.Role)
            ?.Value;
    }

    public static string? GetUserId(this ClaimsPrincipal user)
    {
        return user?.Claims
            .FirstOrDefault(c => c.Type == "uid")
            ?.Value;
    }
}
