﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Extensions;
using Teletalk.Services.Models.AgentSubmissionDTOS;
using Teletalk.Services.Models.CampaignModels;
using Teletalk.Services.Models.QualitySheetModels;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Teletalk.Services.Services.QualitySheetServices
{
    public class QualitySheetService : IQualitySheetService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly UserManager<ApplicationUsers> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public QualitySheetService(IRepositoryManager repository, IMapper mapper, UserManager<ApplicationUsers> userManager, 
            IHttpContextAccessor httpContextAccessor)
        {
            _mapper = mapper;
            _repository = repository;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<MiddlewareObject<bool>> AddQualitySheet(AddQualitySheet qualitySheetDetails)
        { 
            try 
            {
                var agent = await _userManager.FindByIdAsync(qualitySheetDetails.UserId);
                if (agent == null)
                {
                    return new MiddlewareObject<bool>(null, "Agent not found")
                    {
                        IsSuccess = false,
                        StatusCode = AppStatics.Not_found
                    };
                }

                var newSheet = _mapper.Map<QualitySheet>(qualitySheetDetails);
                
                newSheet.AgentId = agent.Id;
                newSheet.SalesAgentName = agent.UserName;
                
                _repository.QualitySheetRepo.Create(newSheet);
                await _repository.SaveAsync();
                
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex) 
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
            
        }

        public async Task<MiddlewareObject<List<GetAllQualitySheets>>> GetAllQualitySheets(GetQuailtySheetsByAgentId req)
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();
                
                if (role == "Admin" || role == "Quality")
                {
                    var query = await _repository.QualitySheetRepo
                     .FindByCondition(q => q.CoachingDate.Date >= req.From.Date
                         && q.CoachingDate.Date <= req.To.Date, false)
                     .Include(q => q.Agent).ToListAsync();

                    var count = query.Count();

                    query = query.Skip((req.PageNumber - 1) * req.PageSize).Take(req.PageSize).ToList();

                    var result = _mapper.Map<List<GetAllQualitySheets>>(query);
                    return new MiddlewareObject<List<GetAllQualitySheets>>(result, "Success") { Count = count };
                } 
                else if (role == "TeamLeader")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();
                    var TeamLeaderEntity = await _userManager.FindByIdAsync(userId);

                    var agentIds = await _userManager.Users
                        .Where(u => u.TeamNumber == TeamLeaderEntity.TeamNumber && u.isActive && u.CampaignId == TeamLeaderEntity.CampaignId)
                        .Select(x => x.Id)
                        .ToListAsync();

                    var query = await _repository.QualitySheetRepo
                      .FindByCondition(q => q.CoachingDate.Date >= req.From.Date
                          && q.CoachingDate.Date <= req.To.Date
                          && agentIds.Contains(q.AgentId), false)
                      .Include(q => q.Agent).ToListAsync();

                    var count = query.Count();

                    query = query.Skip((req.PageNumber - 1) * req.PageSize).Take(req.PageSize).ToList();

                    var result = _mapper.Map<List<GetAllQualitySheets>>(query);
                    return new MiddlewareObject<List<GetAllQualitySheets>>(result, "Success") { Count = count };
                }
                else if (role == "Agent")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();
                    
                    var query = await _repository.QualitySheetRepo
                       .FindByCondition(q => q.CoachingDate.Date >= req.From.Date
                           && q.CoachingDate.Date <= req.To.Date
                           && q.Agent.Id == userId, false)
                       .Include(q => q.Agent).ToListAsync();

                    var count = query.Count();

                    query = query.Skip((req.PageNumber - 1) * req.PageSize).Take(req.PageSize).ToList();

                    var result = _mapper.Map<List<GetAllQualitySheets>>(query);
                    return new MiddlewareObject<List<GetAllQualitySheets>>(result, "Success") { Count = count };
                }
                return new MiddlewareObject<List<GetAllQualitySheets>>((List<GetAllQualitySheets>)null, "No Data") { IsSuccess = true};
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<GetAllQualitySheets>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<QualitySheetDetails>> GetQualitySheetDetails(Guid id)
        {
            try
            {
                var query = await _repository.QualitySheetRepo.FindByCondition(x=>x.Id.Equals(id),false).Include(q=>q.Agent).FirstOrDefaultAsync();
                var result = _mapper.Map<QualitySheetDetails>(query);
                return new MiddlewareObject<QualitySheetDetails>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<QualitySheetDetails>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }

        }

        public async Task<MiddlewareObject<bool>> UpdateQualitySheetAsync(Guid id, UpdateQualitySheetModel updateDto)
        {
            try
            {
                var qualitySheet = await _repository.QualitySheetRepo.FindByCondition(q => q.Id == id, true).Include(q=>q.Agent).FirstOrDefaultAsync();
                if (qualitySheet == null)
                {
                    return new MiddlewareObject<bool>(false, "QualitySheet not found")
                    {
                        IsSuccess = false,
                        StatusCode = AppStatics.Not_found
                    };
                }

                var updated= _mapper.Map(updateDto, qualitySheet);
                _repository.QualitySheetRepo.Update(updated);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "QualitySheet updated successfully"){IsSuccess = true};
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(false, "Something went wrong while updating the QualitySheet")
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }
        }

        public async Task<MiddlewareObject<List<QualityRatedAgentsLookup>>> GetAgentsWithQualitySheetsUsernames()
        {
            try
            {
                var agentIds = await _repository.QualitySheetRepo.FindAll(false)
                    .Select(q => q.AgentId).Distinct().ToListAsync();

                var agentsWithQualitySheets = await _userManager.Users.Where(u => agentIds.Contains(u.Id))
                    .Select(u => u.UserName)
                    .ToListAsync();

                var result = _mapper.Map<List<QualityRatedAgentsLookup>>(agentsWithQualitySheets);

                return new MiddlewareObject<List<QualityRatedAgentsLookup>>(result, "Success")
                {
                    Count = agentsWithQualitySheets.Count
                };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<QualityRatedAgentsLookup>>(error: null, "Something went wrong")
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }
        }
    }
}
