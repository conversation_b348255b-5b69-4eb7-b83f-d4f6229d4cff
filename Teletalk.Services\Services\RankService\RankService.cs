﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Models.Enums;
using Teletalk.Services.Common;
using Teletalk.Services.Models.RankModels;

namespace Teletalk.Services.Services.RankService
{
    public class RankService : IRankService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly UserManager<ApplicationUsers> _UserManager;
        public RankService(IRepositoryManager repository,
            IMapper mapper, 
            UserManager<ApplicationUsers> userManager)
        {
            _repository = repository;
            _mapper = mapper;
            _UserManager = userManager;
        }
        public async Task<MiddlewareObject<List<GetTopRankModelRes>>> GetAgentsTopRank()
        {
            try
            {
                // getting the agents who are in the role of Agent
                var Agents = await _UserManager.GetUsersInRoleAsync("Agent");

                if (!Agents.Any())
                    return new MiddlewareObject<List<GetTopRankModelRes>>((List<GetTopRankModelRes>)null, "No Agents") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

                var AgentsId = Agents.Select(x => x.Id).ToList();

                // get the agents from "AgentSubmission" collection
                var agentSubmissions = _repository.AgentSubmission
                    .FindByCondition(x => AgentsId.Contains(x.AgentId) && x.status == Status.Approved, false)
                    .ToList();

                if (!agentSubmissions.Any())
                    return new MiddlewareObject<List<GetTopRankModelRes>>((List<GetTopRankModelRes>)null, "No Submission") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

                var result = agentSubmissions
                .GroupBy(g => g.AgentId)
                .Select(g => new
                {
                    AgentId = g.Key,
                    TotalSubmissions = g.Count(),
                    FirstSubmission = g.First() // To get CampaignId etc.
                })
                .OrderByDescending(x => x.TotalSubmissions)
                .Select((x, index) => new GetTopRankModelRes
                {
                    AgentId = x.AgentId,
                    AgentName = Agents.FirstOrDefault(a => a.Id == x.AgentId)?.UserName ?? "Unknown",
                    CampaignId = x.FirstSubmission.CampaignId,
                    CampaignName = x.FirstSubmission.Campaign?.CampaignName ?? "Unknown",
                    TeamLeaderNumber = x.FirstSubmission.TeamNumber,
                    Rank = index + 1
                })
                .ToList();

                return new MiddlewareObject<List<GetTopRankModelRes>>(result);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<GetTopRankModelRes>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<GetTopRankModelRes>> GetTopAgentByDailySales()
        {
            try
            {
                // getting the agents who are in the role of Agent
                var Agents = await _UserManager.GetUsersInRoleAsync("Agent");

                if (!Agents.Any())
                    return new MiddlewareObject<GetTopRankModelRes>((GetTopRankModelRes)null, "No Agents") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

                var AgentsId = Agents.Select(x => x.Id).ToList();

                // get the agents from "AgentSubmission" collection for the current day
                var agentSubmissions = _repository.AgentSubmission
                    .FindByCondition(x => AgentsId.Contains(x.AgentId) && x.status == Status.Approved && x.CreatedDate.Date == DateTime.Today, false)
                    .ToList();

                if (!agentSubmissions.Any())
                    return new MiddlewareObject<GetTopRankModelRes>((GetTopRankModelRes)null, "No Submission for today") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

                var result = agentSubmissions
                .GroupBy(g => g.AgentId)
                .Select(g => new
                {
                    AgentId = g.Key,
                    TotalSubmissions = g.Count(),
                    FirstSubmission = g.First() // To get CampaignId etc.
                })
                .OrderByDescending(x => x.TotalSubmissions)
                .Select((x, index) => new GetTopRankModelRes
                {
                    AgentId = x.AgentId,
                    AgentName = Agents.FirstOrDefault(a => a.Id == x.AgentId)?.UserName ?? "Unknown",
                    CampaignId = x.FirstSubmission.CampaignId,
                    CampaignName = x.FirstSubmission.Campaign?.CampaignName ?? "Unknown",
                    TeamLeaderNumber = x.FirstSubmission.TeamNumber,
                    Rank = index + 1
                })
                .FirstOrDefault();

                return new MiddlewareObject<GetTopRankModelRes>(result);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<GetTopRankModelRes>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
