﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Context;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.GenreicRepo;

namespace Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.MediaRepo
{
    public class MediaRepo :RepositoryBase<Media>,IMediaRepo
    {
        public MediaRepo(ApplicationDbContext context) : base(context)
        { 
        }
    }
}
