﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.AttendanceModels;

namespace Teletalk.Services.Services.AttendanceServices
{
    public class AttendanceService : IAttendanceServices
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly UserManager<ApplicationUsers> _userManager;
        public AttendanceService(IRepositoryManager repository,IMapper mapper,UserManager<ApplicationUsers> userManager)
        {
            _repository = repository;
            _mapper = mapper;
            _userManager = userManager;
        }

        public async Task<MiddlewareObject<bool>> AddAttendanceWithLogin(ApplicationUsers user)
        {
            try
            {
                var attendance = new Attendance
                {
                    EmployeeName = user.UserName,
                    Title = user.Title,
                    SigninTime = DateTime.Now,
                    User = user,
                    IsDelete = false,
                    CreatedBy = user.UserName
                };

                _repository.AttendanceRepository.Create(attendance);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(data: true) { IsSuccess = true, StatusCode = AppStatics.Success_Status_Code };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error:null,message:ex.Message)
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }
        }
        private decimal CalculateDelay(DateTime signInTime)
        {
            var dayStartDateTime = signInTime.Date.Add(AppStatics.DayStartTime);
            var delay = signInTime > dayStartDateTime
                ? (decimal)(signInTime - dayStartDateTime).TotalMinutes
                : 0m;

            return  delay;
        }
    
        public async Task<MiddlewareObject<List<GetAllAttendanceResponse>>> GetAllAttendance(GetAllAttenanceRequest req)
        {
            try
            {
                var attend = await _repository.AttendanceRepository.FindAll(false)
                    .SearchForAttendanceUserName(req).ToListAsync();

                var attendances = attend
                    .Skip((req.PageNumber - 1) * req.PageSize)
                    .Take(req.PageSize).ToList();
                    
                var Result = _mapper.Map<List<GetAllAttendanceResponse>>(attendances);
                return new MiddlewareObject<List<GetAllAttendanceResponse>>(Result, "Success") { Count = attend.Count() };
            }
            catch (Exception)
            {
                return new MiddlewareObject<List<GetAllAttendanceResponse>>(error: null, message: "something went wrong") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> UpdateAttendance(string id, UpdateAttendanceModel model)
        {
            try
            {
                var attendanceEntity = await _repository.AttendanceRepository
                    .FindByCondition(x => x.Id.ToString() == id, true)
                    .FirstOrDefaultAsync();

                attendanceEntity.Deduction = model.Deduction;
                attendanceEntity.Delay = model.Delay;
                await _repository.SaveAsync();

                return new MiddlewareObject<bool>() { Data = true, IsSuccess = true, StatusCode = AppStatics.Success_Status_Code };
            }
            catch (Exception)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }

        }
    }
}
