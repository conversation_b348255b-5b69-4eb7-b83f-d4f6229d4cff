﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Infrastructure.Repos.AgentSubmissonRepo;
using Teletalk.DAL.Infrastructure.Repos.AttendanceRepo;
using Teletalk.DAL.Infrastructure.Repos.CampaignRepository;
using Teletalk.DAL.Infrastructure.Repos.FormDatatypeRepository;
using Teletalk.DAL.Infrastructure.Repos.InterviewSubmissionRepository;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.LaptopsRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.MediaRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.NetworkDevicesRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.ServersRepo;
using Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.SoftwareRepo;
using Teletalk.DAL.Infrastructure.Repos.PermissionRepository;
using Teletalk.DAL.Infrastructure.Repos.PermissionRoleRepository;
using Teletalk.DAL.Infrastructure.Repos.TeamLeaderSubmissionRepository;
using Teletalk.DAL.Infrastructure.Repos.QualitySeetRepos;
using Teletalk.DAL.Infrastructure.Repos.TeamNumberCampaignRepository;
using Teletalk.DAL.Infrastructure.Repos.PaySlipRepository;

namespace Teletalk.DAL.Infrastructure.RepositoryManager
{
    public interface IRepositoryManager
    {
        IAgentSubmissionRepository AgentSubmission { get; }
        IPermissionRoleRepository permissionRoleRepository { get; }
        IPermissionRepository permissionRepository { get; }
        ICampaignRepository CampaignRepository { get; }
        IFormDatatypesRepository FormDatatypeRepository { get; }
        ILaptopsRepo LaptopsRepo { get; }
        IMediaRepo MediaRepo { get; }
        IServersRepo ServersRepo { get; }
        ISoftwareRepo SoftwareRepo { get; }
        INetworkDevicesRepo NetworkDevicesRepo { get; }
        IAttendanceRepository AttendanceRepository { get; }
        IInterviewSubmissionRepository InterviewSubmissionRepository { get; }
        ITeamNumberCampaignRepo TeamNumberCampaignRepo { get; }
        ITeamLeaderSubmissionRepo TeamLeaderSubmissionRepo { get; }
        IQualitySheetRepo QualitySheetRepo { get; }
        IPaySlipRepository PaySlipRepository { get; }
        Task SaveAsync();
    }
}
