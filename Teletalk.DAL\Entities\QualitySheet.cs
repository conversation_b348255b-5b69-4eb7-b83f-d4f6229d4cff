﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.DAL.Entities
{
    public class QualitySheet
    {
        public Guid Id { get; set; }
        public string SalesAgentName { get; set; }
        public DateTime CoachingDate { get; set; } = DateTime.Now;
        public string CoachingFocusArea { get; set; }
        public string CurrentPerfomranceReview { get; set; }
        public string StrengthAndWweaknesess { get; set; }
        public string ActionPlan { get; set; }
        public string LeaderComment { get; set; }
        public string AgentId { get; set; }
        public ApplicationUsers Agent { get; set; }

        public bool IsDelete { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
    }
}
