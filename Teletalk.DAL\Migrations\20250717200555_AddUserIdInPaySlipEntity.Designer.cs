﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Teletalk.DAL.Context;

#nullable disable

namespace Teletalk.DAL.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250717200555_AddUserIdInPaySlipEntity")]
    partial class AddUserIdInPaySlipEntity
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.31")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.AgentSubmission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AgentId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SubmissionForm")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TeamNumber")
                        .HasColumnType("int");

                    b.Property<int>("status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.HasIndex("CampaignId");

                    b.ToTable("AgentSubmissions");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.ApplicationUsers", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("Branch")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CampaignId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Deviceid")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Gender")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IdNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TeamNumber")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("isActive")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Attendance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Deduction")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Delay")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("EmployeeName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("SignOutTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("SigninTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Attendances");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Campaign", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CampaignName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CampaignNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SubmissionForm")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Campaign");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.InterviewSubmission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AnyRecommendation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Appearance")
                        .HasColumnType("int");

                    b.Property<DateTime>("BrithOfDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Degree")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EnglishFluency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("EnglishLanguage")
                        .HasColumnType("int");

                    b.Property<int>("InterActionSkills")
                        .HasColumnType("int");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("Major")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MaritalStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MilitaryStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Minor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mobile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NationalId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Personality")
                        .HasColumnType("int");

                    b.Property<string>("PostGrad")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PresentAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Reading")
                        .HasColumnType("int");

                    b.Property<string>("ReferredToUs")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SelfConfidence")
                        .HasColumnType("int");

                    b.Property<int>("SellingSkills")
                        .HasColumnType("int");

                    b.Property<bool?>("Status")
                        .HasColumnType("bit");

                    b.Property<int>("Tone")
                        .HasColumnType("int");

                    b.Property<string>("UniversityName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkExperience")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("InterviewSubmissions");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Laptops", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AntiVirusLastUpdated")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ApplicationBusinessSpecificUse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AvailabilityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BackUpFrequency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BackUpMethod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CPU")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConfidentialityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Custodian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeviceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HDD")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IntegrityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Owner")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RAM")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("User")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhetherUsedOutOfPremises")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Laptops");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Media", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApplicationBusinessSpecificUse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AvailabilityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BackUpFrequency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BackUpMethod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Capacity")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConfidentialityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Custodian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeviceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IntegrityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Owner")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RestorationCheck")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StoredInformationAssets")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("User")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhetherStoredOutOfPremises")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Media");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.NetworkDevices", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApplicationUse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AvailabilityRequirements")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConfidentialityRequirements")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Custodian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeviceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HostName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IOS")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IntegrityRequirements")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MaintenanceStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NettedIP")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Owner")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Users")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("NetworkDevices");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Payslip", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Bonus")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Commisions")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateFrom")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateTo")
                        .HasColumnType("datetime2");

                    b.Property<string>("Deduction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmployeeName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NumberOfSales")
                        .HasColumnType("int");

                    b.Property<string>("OtherDeduction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Percentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Salary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Target")
                        .HasColumnType("int");

                    b.Property<decimal>("TotalSalary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Payslips");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.PermissionRoles", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserID")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.ToTable("PermissionRoles");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Permissions", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Icon")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Path")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PermissionName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Permissions");

                    b.HasData(
                        new
                        {
                            Id = new Guid("c6dff7c3-1ea1-40f7-99d2-551ac9fcff00"),
                            Icon = "dashboard",
                            Path = "/dashboard/agent/agentdashboard",
                            PermissionName = "Dashboard"
                        },
                        new
                        {
                            Id = new Guid("d08b33d0-be66-409a-8cf1-d505e8174bb9"),
                            Icon = "profile",
                            Path = "/dashboard/agent/agenttargetform",
                            PermissionName = "Target Form"
                        },
                        new
                        {
                            Id = new Guid("c18fadb1-5c66-4f01-9bcb-5a70c83cd933"),
                            Icon = "database",
                            Path = "/dashboard/agent/agentstored",
                            PermissionName = "Stored Data"
                        },
                        new
                        {
                            Id = new Guid("dde025e0-84bd-4bac-a782-be0ff0243a47"),
                            Icon = "save",
                            Path = "/dashboard/agent/agentqualitysheet",
                            PermissionName = "Quality Sheet"
                        },
                        new
                        {
                            Id = new Guid("b3629e21-f614-425b-b658-efd511615c11"),
                            Icon = "dashboard",
                            Path = "/teamleaderdashboard",
                            PermissionName = "Dashboard"
                        },
                        new
                        {
                            Id = new Guid("aeb4d2cb-36a3-464b-93a8-21a48dc574a4"),
                            Icon = "picture",
                            Path = "/teamleadertargetform",
                            PermissionName = "Target Form"
                        },
                        new
                        {
                            Id = new Guid("4a557104-2b92-4f25-8b33-ea44ed0c905b"),
                            Icon = "database",
                            Path = "/teamleaderstoreddata",
                            PermissionName = "Stored Data"
                        },
                        new
                        {
                            Id = new Guid("24c6642e-3221-4ea4-a811-921315c7fe05"),
                            Icon = "check-square",
                            Path = "/teamleadersalesstatus",
                            PermissionName = "Sales Status"
                        },
                        new
                        {
                            Id = new Guid("673390f5-a7ca-4590-bb53-e322c869f283"),
                            Icon = "carry-out",
                            Path = "/hrattendance",
                            PermissionName = "Attendance"
                        },
                        new
                        {
                            Id = new Guid("82ab9a99-31c5-4aac-9b33-090ef178c343"),
                            Icon = "dollar",
                            Path = "/dashboard/hr/hrpayslip",
                            PermissionName = "Payslip"
                        },
                        new
                        {
                            Id = new Guid("bb034fad-6a61-4984-8435-e17dc6477d3b"),
                            Icon = "profile",
                            Path = "/hrinterviewapplication",
                            PermissionName = "Interview Application"
                        },
                        new
                        {
                            Id = new Guid("93d2cb2f-1847-45cc-993a-242b0baaca53"),
                            Icon = "dashboard",
                            Path = "/hrinterviewdashboard",
                            PermissionName = "Interview Dashboard"
                        },
                        new
                        {
                            Id = new Guid("196100ce-7c72-4292-a9bf-a3d419782851"),
                            Icon = "database",
                            Path = "/dashboard/agent/quailtystoreddata",
                            PermissionName = "Stored Data"
                        },
                        new
                        {
                            Id = new Guid("6a87ace4-9dec-4672-b153-3b7cf0d6e8c2"),
                            Icon = "profile",
                            Path = "/dashboard/quality/qualitysheet",
                            PermissionName = "Quality Sheet"
                        },
                        new
                        {
                            Id = new Guid("6441879c-b217-4711-9d8d-24f4f1a8794f"),
                            Icon = "stock",
                            Path = "/dashboard/admin/adminitstock",
                            PermissionName = "IT Stock"
                        },
                        new
                        {
                            Id = new Guid("458384ef-110c-43eb-a8fa-5041f4c8172c"),
                            Icon = "database",
                            Path = "/dashboard/admin/adminstoreddata",
                            PermissionName = "Stored Data"
                        },
                        new
                        {
                            Id = new Guid("0147457e-a83f-4589-952c-39c3523d4f49"),
                            Icon = "to-top",
                            Path = "/dashboard/admin/adminagentsrank",
                            PermissionName = "Agent's Rank"
                        },
                        new
                        {
                            Id = new Guid("ba63a002-4d1c-472c-99dc-4816c81153e9"),
                            Icon = "usergroup-add",
                            Path = "/dashboard/admin/adminaccesscontrol",
                            PermissionName = "Access Control"
                        },
                        new
                        {
                            Id = new Guid("f7764f21-dfac-465f-a884-4e5649436ea7"),
                            Icon = "ungroup",
                            Path = "/dashboard/admin/admincampaign",
                            PermissionName = "Campaign"
                        });
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.QualitySheet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ActionPlan")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AgentId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CoachingDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CoachingFocusArea")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrentPerfomranceReview")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("LeaderComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SalesAgentName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StrengthAndWweaknesess")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.ToTable("QualitySheets");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Servers", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApplicationBusinessSpecificRequirements")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AvailabilityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BackupSchedule")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CPU")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConfidentialityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Custodian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HDD")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HostName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IntegrityRequirementsForDataStored")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MaintenanceStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OS")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Owner")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PurposeServiceRole")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RAM")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RackNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ServerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ServicePacksRequired")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SlotNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SoftwareApplicationDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StoredInformationAssets")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Users")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VirtualServer")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Servers");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Software", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApplicationUser")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AvailabilityRequirementsForDataProcessed")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConfidentialityRequirementsForDataProcessed")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactDetaislVendor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Custodian")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IntegrityRequirementsForDataProcessed")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenseDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MaintenanceStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NoOfUsers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Owner")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Used")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Users")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VendorName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Software");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.TeamLeaderSubmission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("DailyTarget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("MonthlyTarget")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("TeamLeaderSubmissions");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.TeamNumberCampaign", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TeamNumber")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.ToTable("TeamNumberCampaigns");
                });

            modelBuilder.Entity("Teletalk.DAL.Lookups.FormDataType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("FormDataType");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Text"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Number"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Date"
                        },
                        new
                        {
                            Id = 4,
                            Name = "PhoneNumber"
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.AgentSubmission", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", "Agent")
                        .WithMany()
                        .HasForeignKey("AgentId");

                    b.HasOne("Teletalk.DAL.Entities.Campaign", "Campaign")
                        .WithMany()
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agent");

                    b.Navigation("Campaign");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.ApplicationUsers", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.Campaign", "Campaigns")
                        .WithMany("Users")
                        .HasForeignKey("CampaignId");

                    b.Navigation("Campaigns");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Attendance", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", "User")
                        .WithMany("Attendances")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Payslip", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", "User")
                        .WithMany("Payslips")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.PermissionRoles", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.Permissions", "Permission")
                        .WithMany("permissionRoles")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.QualitySheet", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", "Agent")
                        .WithMany("QualitySheets")
                        .HasForeignKey("AgentId");

                    b.Navigation("Agent");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.TeamLeaderSubmission", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.ApplicationUsers", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.TeamNumberCampaign", b =>
                {
                    b.HasOne("Teletalk.DAL.Entities.Campaign", "Campaign")
                        .WithMany("teamNumberCampaigns")
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.ApplicationUsers", b =>
                {
                    b.Navigation("Attendances");

                    b.Navigation("Payslips");

                    b.Navigation("QualitySheets");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Campaign", b =>
                {
                    b.Navigation("Users");

                    b.Navigation("teamNumberCampaigns");
                });

            modelBuilder.Entity("Teletalk.DAL.Entities.Permissions", b =>
                {
                    b.Navigation("permissionRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
