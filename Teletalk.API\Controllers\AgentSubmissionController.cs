﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.AgentSubmissionDTOS;
using Teletalk.Services.Models.CampaignModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AgentSubmissionController : ControllerBase
    {
        private readonly IServiceManager _service;
        public AgentSubmissionController(IServiceManager service) => _service = service;

        [Authorize(Roles= "Agent")]
        [HttpPost("add-submission")]
        public async Task<IActionResult> AddSubmission(string submissionForm, string userId, string campaignId)
        {
            var submit = await _service.AgentSubmissionService.AddAgentSubmissionService(submissionForm, userId, campaignId);
            return Ok("Submitted Successfully");
        }

        [HttpGet("{UserId}")]
        public async Task<IActionResult> GetSubmissionFormByAgentId(string UserId)
        {
            var result = await _service.AgentSubmissionService.GetSubmissionFormByAgentId(UserId);
            return Ok(result);
        }

        [HttpGet("get-submission")]
        public async Task<MiddlewareObject<List<AgentSubmissionDTO>>> GetSubmissionFormByTeamLeadertId(string teamleader)
        {
            var result = await _service.AgentSubmissionService.GetAgentSubmissionsByUserIdAsync(teamleader);
            return result;
        }

        [HttpGet("get-submitted-form-by-id")]
        public async Task<MiddlewareObject<List<Submittedform>>> GetSubmittedFormFieldsById(string submissionId)
        {
            var result = await _service.AgentSubmissionService.GetSubmittedFormById(submissionId);
            return result;
        }
        [HttpPost("get-agents-by-teamNumber")]
        public async Task<IActionResult> GetAgentsByTeamNumber(GetAgentsbyTeamLeaderReq req)
        {
            var result = await _service.AgentSubmissionService.GetAgentsByTeamNumber(req);
            return Ok(result);
        }
        [Authorize(Roles = "Agent")] 
        [HttpPost("get-agent-submissions-by-status")]
        public async Task<IActionResult> GetAgentSubmissionsByStatus(GetAgentSubmissionByStatusReq req)
        {
            // refactored to use the service layer
            var result = await _service.AgentSubmissionService.GetAgentSubmissionByStatus(req);
            return Ok(result);
        }
        [HttpPost("get-submissions-by-campaign-id")]
        public async Task<IActionResult> GetSubmissionsByCampaignId(GetAgentSubmissionsByCampaignIdReq req)
        {
             var result = await _service.AgentSubmissionService.GetAgentSubmissionsByCampaignId(req);
            return Ok(result);
        }



        [Authorize(Roles = "Agent")] // ✅ Correct - 
        [HttpPost("get-agent-submission-graph")]
        public async Task<IActionResult> GetAgentSubmissionGraph(GetAgentSubmissionGraphReq req)
        {
            var result = await _service.AgentSubmissionService.GetAgentSubmissionGraph(req);
            return Ok(result);
        }
    }
}
