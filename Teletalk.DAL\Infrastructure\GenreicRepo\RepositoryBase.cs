﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Teletalk.DAL.Context;

namespace Teletalk.DAL.Infrastructure.GenreicRepo
{
    public class RepositoryBase<T> : IRepositoryBase<T> where T : class
    {
        protected ApplicationDbContext _context;
        public RepositoryBase(ApplicationDbContext repositoryContext)
        => _context = repositoryContext;

        public IQueryable<T> FindAll(bool trackChanges) => !trackChanges ? _context.Set<T>().AsNoTracking()
            : _context.Set<T>();
        public IQueryable<T> FindByCondition(Expression<Func<T, bool>> expression, bool trackChanges)
            => !trackChanges ? _context.Set<T>().Where(expression).AsNoTracking() :
                                _context.Set<T>().Where(expression);
        public void Create(T entity) => _context.Set<T>().Add(entity);
        public void CreateRange(IEnumerable<T> entity) => _context.Set<T>().AddRange(entity);
        public void Update(T entity) => _context.Set<T>().Update(entity);
        public void Delete(T entity) => _context.Set<T>().Remove(entity);
        public void DeleteRange(IEnumerable<T> entities) => _context.Set<T>().RemoveRange(entities);
    }
}
