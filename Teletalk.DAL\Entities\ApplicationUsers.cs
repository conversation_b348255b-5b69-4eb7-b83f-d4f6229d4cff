﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;

namespace Teletalk.DAL.Entities
{
    public class ApplicationUsers : IdentityUser
    {
        public string FullName { get; set; }
        public string Title { get; set; }
        public int? TeamNumber { get; set; }
        public string Branch { get; set; }
        public string Deviceid { get; set; }
        public string Gender { get; set; }
        public bool isActive { get; set; } = true;
        public string IdNumber { get; set; }
        public ICollection<QualitySheet> QualitySheets { get; set; }
        public ICollection<Attendance> Attendances { get; set; }
        public ICollection<Payslip> Payslips { get; set; }

        // Foreign key
        public Guid? CampaignId { get; set; }
        // Navigation Property
        public Campaign? Campaigns { get; set; }
    }
}
