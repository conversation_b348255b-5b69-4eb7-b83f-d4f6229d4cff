﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.CampaignModels;

namespace Teletalk.Services.Services.CampaignServices
{
    public class CampaignService : ICampaignService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public CampaignService(IRepositoryManager repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> AddCampaign(AddCampaignRequest request)
        {
            try
            {
                var newCampaign = _mapper.Map<Campaign>(request);
                newCampaign.CampaignName = request.label;    
                _repository.CampaignRepository.Create(newCampaign);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<CampaignModel>>> GetAllCampaigns()
        {
            try
            {
                var query = await _repository.CampaignRepository.FindAll(false).Where(x => x.IsDelete != true).ToListAsync();
                var result = _mapper.Map<List<CampaignModel>>(query);
                return new MiddlewareObject<List<CampaignModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<CampaignModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<CampaignModel>>> GetActiveCampaign()
        {
            try
            {
                var query = await _repository.CampaignRepository.FindByCondition(x => x.IsActive == true && x.IsDelete != true, false).ToListAsync();
                var result = _mapper.Map<List<CampaignModel>>(query);
                return new MiddlewareObject<List<CampaignModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<CampaignModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<FormDataTypesModel>>> GetDataTypes()
        {
            try
            {
                var query = await _repository.FormDatatypeRepository.FindAll(false).ToListAsync();
                var result = _mapper.Map<List<FormDataTypesModel>>(query);
                return new MiddlewareObject<List<FormDataTypesModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<FormDataTypesModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> UpdateCampaign(Guid id, CampaginForUpdateModel dto)
        {
            try
            {
                var query = await _repository.CampaignRepository.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                var UpdatedCampagin = _mapper.Map(dto, query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Updated successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> DeleteCampaign(Guid id)
        {
            try
            {
                var query = await _repository.CampaignRepository.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                query.IsDelete = true;
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Deleted Successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        //public async Task<MiddlewareObject<bool>> UpdateTeamNumber(Guid id, UpdateTeamNumberModel req)
        //{
        //    try
        //    {
        //        var query = await _repository.CampaignRepository.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
        //        query.TeamNumber = req.TeamNumber;
        //        await _repository.SaveAsync();
        //        return new MiddlewareObject<bool>(true, "Updated successfully");
        //    }
        //    catch (Exception ex)
        //    {
        //        return new MiddlewareObject<bool>(error: null, message: "something went wrong")
        //        { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
        //    }
        //}
     
        public async Task<MiddlewareObject<GetCampaignByIdRes>> GetCampaignById(Guid id)
        {
            try
            {
                var query = await _repository.CampaignRepository.FindByCondition(x => x.Id == id && x.IsDelete != true, false).FirstOrDefaultAsync();

                var SubmissionForm = System.Text.Json.JsonSerializer.Deserialize<List<CampaignSubmissionForm>>(query.SubmissionForm);
                if (query == null)
                {
                    return new MiddlewareObject<GetCampaignByIdRes>((GetCampaignByIdRes)null, message: $"No Campaign with {id}")
                    { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
                }

                var result = _mapper.Map<GetCampaignByIdRes>(query);
                result.SubmissionField = SubmissionForm;
                return new MiddlewareObject<GetCampaignByIdRes>(result);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<GetCampaignByIdRes>((GetCampaignByIdRes)null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
