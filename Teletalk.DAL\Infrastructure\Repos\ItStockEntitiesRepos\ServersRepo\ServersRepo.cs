﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Context;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.GenreicRepo;

namespace Teletalk.DAL.Infrastructure.Repos.ItStockEntitiesRepos.ServersRepo
{
    internal class ServersRepo : RepositoryBase<Servers>,IServersRepo
    {
        public ServersRepo(ApplicationDbContext context) : base(context)
        {

        }
    }
}
