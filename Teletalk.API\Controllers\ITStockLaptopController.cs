﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.LaptopsModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ITStockLaptopController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public ITStockLaptopController(IServiceManager serviceManager) => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddLaptop([FromBody] AddLaptopRequest request)
        {
            var result = await _serviceManager.LaptopsService.AddLaptop(request);
            return result;
        }
        [HttpGet()]
        public async Task<MiddlewareObject<List<LaptopsModel>>> GetAllCampaigns()
        {
            var result = await _serviceManager.LaptopsService.GetAllLaptops();
            return result;
        }
        [HttpDelete()]
        public async Task<MiddlewareObject<bool>> Deletelaptop(Guid id)
        {
            var result = await _serviceManager.LaptopsService.DeleteLaptop(id);
            return result;
        }
        [HttpPut()]
        public async Task<MiddlewareObject<bool>> Updatelaptop(Guid id, [FromBody] LaptopforupdateModel dto)
        {
            var result = await _serviceManager.LaptopsService.UpdateLaptop(id, dto);
            return result;
        }
        // Get By Id
        [HttpGet("{id}")]
        public async Task<MiddlewareObject<LaptopsModel>> GetLaptopById(Guid id)
        {
            var result = await _serviceManager.LaptopsService.GetLaptopById(id);
            return result;
        }
    }
}
