﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.Services.Models.ItStockModels.SoftwareModels
{
    public class AddSoftwareRequest
    {
        public string Description { get; set; }
        public string Version { get; set; }
        public string VendorName { get; set; }
        public string ContactDetaislVendor { get; set; }
        public string Owner { get; set; }
        public string Custodian { get; set; }
        public string Users { get; set; }
        public string SerialNumber { get; set; }
        public string Location { get; set; } //Device Type
        public string LicenseDetails { get; set; }
        public string NoOfUsers { get; set; }
        public string Used { get; set; }
        public string ApplicationUser { get; set; }
        public string MaintenanceStatus { get; set; }
        public string ConfidentialityRequirementsForDataProcessed { get; set; }
        public string IntegrityRequirementsForDataProcessed { get; set; }
        public string AvailabilityRequirementsForDataProcessed { get; set; }
    }
}
