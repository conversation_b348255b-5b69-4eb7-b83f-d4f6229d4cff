﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.Services.Models.ItStockModels.ServersModels
{
    public class AddServerRequest
    {
        public string ServerName { get; set; }
        public string Owner { get; set; }
        public string Custodian { get; set; }
        public string Users { get; set; }
        public string AssetID { get; set; }
        public string SerialNumber { get; set; }
        public string IPAddress { get; set; }
        public string RackNumber { get; set; }
        public string SlotNumber { get; set; }
        public string HostName { get; set; }
        public string OS { get; set; }
        public string ServicePacksRequired { get; set; }
        public string SoftwareApplicationDetails { get; set; }
        public string VirtualServer { get; set; }
        public string ApplicationBusinessSpecificRequirements { get; set; }
        public string MaintenanceStatus { get; set; }
        public string Model { get; set; }
        public string CPU { get; set; }
        public string RAM { get; set; }
        public string HDD { get; set; }
        public string PurposeServiceRole { get; set; }
        public string StoredInformationAssets { get; set; }
        public string BackupSchedule { get; set; }
        public string ConfidentialityRequirementsForDataStored { get; set; }
        public string IntegrityRequirementsForDataStored { get; set; }
        public string AvailabilityRequirementsForDataStored { get; set; }
    }
}
