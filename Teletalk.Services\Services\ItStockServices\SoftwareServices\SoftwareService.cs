﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.ServersModels;
using Teletalk.Services.Models.ItStockModels.SoftwareModels;

namespace Teletalk.Services.Services.ItStockServices.SoftwareServices
{
    public class SoftwareService : ISoftwareService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public SoftwareService(IRepositoryManager repository,IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> AddSoftware(AddSoftwareRequest request)
        {
            try
            {
                var newSoftware = _mapper.Map<Software>(request);
                _repository.SoftwareRepo.Create(newSoftware);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<SoftwareModel>>> GetAllSoftwares()
        {
            try
            {
                var query = await _repository.SoftwareRepo.FindAll(false).ToListAsync();
                var result = _mapper.Map<List<SoftwareModel>>(query);
                return new MiddlewareObject<List<SoftwareModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<SoftwareModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<bool>> DeleteSoftware(Guid id)
        {
            try
            {
                var query = await _repository.SoftwareRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                _repository.SoftwareRepo.Delete(query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Deleted Successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }


        public async Task<MiddlewareObject<bool>> UpdateSoftware(Guid id, SoftwareForUpdateModel dto)
        {
            try
            {
                var query = await _repository.SoftwareRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                var UpdatedSoftware = _mapper.Map(dto, query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Updated successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<SoftwareModel>> GetSoftwareById(Guid id)
        {
            try
            {
                var query = await _repository.SoftwareRepo.FindByCondition(x => x.Id.Equals(id), false).FirstOrDefaultAsync();
                var result = _mapper.Map<SoftwareModel>(query);
                return new MiddlewareObject<SoftwareModel>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<SoftwareModel>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
