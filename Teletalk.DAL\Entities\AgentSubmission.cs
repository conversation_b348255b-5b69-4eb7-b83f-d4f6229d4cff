﻿using Teletalk.Models.Enums;

namespace Teletalk.DAL.Entities
{
    public class AgentSubmission
    {
        public Guid Id { get; set; }
        public Status status { get; set; } = Status.Pending;
        public string SubmissionForm { get; set; }
        public int? TeamNumber { get; set; }
        public bool IsDelete { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
        public ApplicationUsers Agent { get; set; }
        public string AgentId { get; set; }
        public string AgentName { get; set; }
        public Campaign Campaign { get; set; }
        public Guid CampaignId { get; set; }
    }
}
