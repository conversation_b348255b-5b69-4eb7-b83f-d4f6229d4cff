﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Lookups;

namespace Teletalk.DAL.SeedData
{
    public class DataInitializer : IDataInitializer
    {
        private readonly IServiceProvider _serviceProvider;

        public DataInitializer(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task SeedDataAsync()
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

                var roles = new[] { "Admin", "Hr", "Quality", "TeamLeader", "Agent" };

                foreach (var role in roles)
                {
                    if (!await roleManager.RoleExistsAsync(role))
                    {
                        await roleManager.CreateAsync(new IdentityRole(role));
                    }
                }

            }
        }

        public List<Permissions> Permissions()
        {
            return new List<Permissions>()
            {
                // --------------------------------Agent-----------------------------------
                new Permissions() { Id = new Guid("c6dff7c3-1ea1-40f7-99d2-551ac9fcff00"), PermissionName = "Dashboard", Path = "/dashboard/agent/agentdashboard", Icon = "dashboard" },
                new Permissions() { Id = new Guid("d08b33d0-be66-409a-8cf1-d505e8174bb9"), PermissionName = "Target Form", Path = "/dashboard/agent/agenttargetform", Icon = "profile"},
                new Permissions() { Id = new Guid("c18fadb1-5c66-4f01-9bcb-5a70c83cd933"), PermissionName = "Stored Data", Path = "/dashboard/agent/agentstored", Icon = "database"},
                new Permissions() { Id = new Guid("dde025e0-84bd-4bac-a782-be0ff0243a47"), PermissionName = "Quality Sheet", Path = "/dashboard/agent/agentqualitysheet", Icon = "save"},
                // --------------------------------Team Leader-----------------------------------
                new Permissions() { Id = new Guid("b3629e21-f614-425b-b658-efd511615c11"), PermissionName = "Dashboard", Path = "/teamleaderdashboard", Icon = "dashboard"},
                new Permissions() { Id = new Guid("aeb4d2cb-36a3-464b-93a8-21a48dc574a4"), PermissionName = "Target Form", Path = "/teamleadertargetform",Icon = "picture"},
                new Permissions() { Id = new Guid("4a557104-2b92-4f25-8b33-ea44ed0c905b"), PermissionName = "Stored Data", Path = "/teamleaderstoreddata",Icon = "database"},
                new Permissions() { Id = new Guid("24c6642e-3221-4ea4-a811-921315c7fe05"), PermissionName = "Sales Status", Path = "/teamleadersalesstatus",Icon = "check-square"},
                // --------------------------------Hr-----------------------------------
                new Permissions() { Id = new Guid("673390f5-a7ca-4590-bb53-e322c869f283"), PermissionName = "Attendance", Path = "/hrattendance", Icon = "carry-out"},
                new Permissions() { Id = new Guid("82ab9a99-31c5-4aac-9b33-090ef178c343"), PermissionName = "Payslip", Path = "/dashboard/hr/hrpayslip", Icon = "dollar"},
                new Permissions() { Id = new Guid("bb034fad-6a61-4984-8435-e17dc6477d3b"), PermissionName = "Interview Application", Path = "/hrinterviewapplication", Icon = "profile"},
                new Permissions() { Id = new Guid("93d2cb2f-1847-45cc-993a-242b0baaca53"), PermissionName = "Interview Dashboard", Path = "/hrinterviewdashboard", Icon = "dashboard"},
                // --------------------------------Quality-----------------------------------
                new Permissions() { Id = new Guid("196100ce-7c72-4292-a9bf-a3d419782851"), PermissionName = "Stored Data", Path = "/dashboard/agent/quailtystoreddata", Icon = "database"},
                new Permissions() { Id = new Guid("6a87ace4-9dec-4672-b153-3b7cf0d6e8c2"), PermissionName = "Quality Sheet", Path = "/dashboard/quality/qualitysheet", Icon = "profile"},
                // --------------------------------Admin-----------------------------------
                new Permissions() { Id = new Guid("6441879c-b217-4711-9d8d-24f4f1a8794f"), PermissionName = "IT Stock", Path = "/dashboard/admin/adminitstock", Icon = "stock"},
                new Permissions() { Id = new Guid("458384ef-110c-43eb-a8fa-5041f4c8172c"), PermissionName = "Stored Data", Path = "/dashboard/admin/adminstoreddata",Icon = "database" },
                new Permissions() { Id = new Guid("0147457e-a83f-4589-952c-39c3523d4f49"), PermissionName = "Agent's Rank", Path = "/dashboard/admin/adminagentsrank", Icon = "to-top"},
                new Permissions() { Id = new Guid("ba63a002-4d1c-472c-99dc-4816c81153e9"), PermissionName = "Access Control", Path = "/dashboard/admin/adminaccesscontrol", Icon = "usergroup-add" },
                new Permissions() { Id = new Guid("f7764f21-dfac-465f-a884-4e5649436ea7"), PermissionName = "Campaign", Path = "/dashboard/admin/admincampaign", Icon = "ungroup" },
            };
        }

        public List<FormDataType> DataTypes()
        {
            return new List<FormDataType>()
            {
                new FormDataType() { Id = 1, Name = "Text"},
                new FormDataType() { Id = 2, Name = "Number"},
                new FormDataType() { Id = 3, Name = "Date"},
                new FormDataType() { Id = 4, Name = "PhoneNumber"},
            };
        }
    }
}
