﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.NetworkDevicesModels;
using Teletalk.Services.Models.ItStockModels.ServersModels;

namespace Teletalk.Services.Services.ItStockServices.ServersServices
{
    public interface IServersService
    {
        Task<MiddlewareObject<List<ServerModel>>> GetAllServers();

        Task<MiddlewareObject<bool>> AddServer(AddServerRequest request);

        Task<MiddlewareObject<bool>> DeleteServer(Guid id);

        Task<MiddlewareObject<bool>> UpdateServer(Guid id, ServerForUpdateModel dto);

        Task<MiddlewareObject<ServerModel>> GetServerById(Guid id);
    }
}
