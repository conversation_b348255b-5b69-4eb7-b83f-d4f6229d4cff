﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Lookups;
using Teletalk.DAL.SeedData;

namespace Teletalk.DAL.Context
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUsers>
    {
        private readonly IDataInitializer dataInitializer;
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IDataInitializer dataInitializer) : base(options)
        {
            this.dataInitializer = dataInitializer;
        }
        public DbSet<AgentSubmission> AgentSubmissions { get; set; }
        public DbSet<Attendance> Attendances { get; set; }
        public DbSet<Payslip> Payslips { get; set; }
        public DbSet<PermissionRoles> PermissionRoles { get; set; }
        public DbSet<Permissions> Permissions { get; set; }
        public DbSet<QualitySheet> QualitySheets { get; set; }
        public DbSet<TeamLeaderSubmission> TeamLeaderSubmissions { get; set; }
        public DbSet<Campaign> Campaign { get; set; }
        public DbSet<FormDataType> FormDataType { get; set; }
        public DbSet<Laptops> Laptops { get; set; }
        public DbSet<Media> Media { get; set; }
        public DbSet<NetworkDevices> NetworkDevices { get; set; }
        public DbSet<Servers> Servers { get; set; }
        public DbSet<Software> Software { get; set; }
        public DbSet<InterviewSubmission> InterviewSubmissions { get; set; }
        public DbSet<TeamNumberCampaign> TeamNumberCampaigns { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Permissions>().HasData(dataInitializer.Permissions());
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<FormDataType>().HasData(dataInitializer.DataTypes());
            base.OnModelCreating(modelBuilder);
        }
    }
}
