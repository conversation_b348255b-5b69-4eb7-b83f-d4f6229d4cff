﻿namespace Teletalk.Services.Common
{
    public static class AppStatics
    {
        public static int Success_Status_Code = 200;
        public static int Failure_Status_Code = 0;
        public static int Bad_Request_Status_Code = 400;
        public static int UnAuthorized = 401;
        public static int Not_found = 404;
        public static string IP_END_POINT_URL = "https://api.ipify.org";
        public static string COUNTRY_ISO_CODE_END_POINT_URL = "http://api.ipstack.com/{0}?access_key={1}";
        public static double OTP_TOKEN_EXPIRATION = 10;
        public const string EMAIL_PATTERN = @"^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$";
        public const string PHONE_NUMBER_PATTERN = @"^(05)([0-9]{8})*$|^(\\+\\d{1,3}[- ]?)?\\d{10}";
        const string ASSETS_HELPER_PATH = "AssetsHelper";
        public const string CERTIFICATE_TEMPORARY_PATH = "AssetsHelper\\CertificateTemporary\\";
        public static readonly TimeSpan DayStartTime = new TimeSpan(3, 0, 0); // 3:00 AM


        public static DateTime today = DateTime.Today;
        public static DateTime FIRST_DAY_OF_MONTH = new DateTime(today.Year, today.Month, 1);
        public static DateTime LAST_DAY_OF_MONTH = new DateTime(today.Year, today.Month, DateTime.DaysInMonth(today.Year, today.Month));
    }
}
