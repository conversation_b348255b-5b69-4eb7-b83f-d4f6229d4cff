﻿using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.Services.Common.Helpers
{
    public class ReadJWTToken
    {
        public static string ExtractPayload(string jwtToken)
        {
            var jwtHandler = new JwtSecurityTokenHandler();
            var jwt = jwtHandler.ReadJwtToken(jwtToken);
            return jwt.Payload.SerializeToJson();
        }
    }
}
