﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.DAL.Entities
{
    public class TeamLeaderSubmission
    {
        public Guid Id { get; set; }
        public int MonthlyTarget { get; set; }
        public decimal DailyTarget { get; set; }

        public string UserId { get; set; }
        public ApplicationUsers User { get; set; }

        public bool IsDelete { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
    }
}
