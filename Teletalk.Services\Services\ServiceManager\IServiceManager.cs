﻿using Teletalk.Services.Services.AgentSubmissionService;
using Teletalk.Services.Services.AttendanceServices;
using Teletalk.Services.Services.CampaignServices;
using Teletalk.Services.Services.DashboardService;
using Teletalk.Services.Services.InterviewSubmissionService;
using Teletalk.Services.Services.ItStockServices.LaptopsServices;
using Teletalk.Services.Services.ItStockServices.MediaServices;
using Teletalk.Services.Services.ItStockServices.NetworkDevicesServices;
using Teletalk.Services.Services.ItStockServices.ServersServices;
using Teletalk.Services.Services.ItStockServices.SoftwareServices;
using Teletalk.Services.Services.PayslipService;
using Teletalk.Services.Services.QualitySheetServices;
using Teletalk.Services.Services.RankService;
using Teletalk.Services.Services.TeamCampaignService;
using Teletalk.Services.Services.TeamLeaderServices;

namespace Teletalk.Services.Services.ServiceManager
{
    public interface IServiceManager
    {
        IAgentSubmissionServices AgentSubmissionService { get; }
        ICampaignService CampaignService { get; }
        ILaptopsService LaptopsService { get; }
        IMediaService MediaService { get; }
        INetworkDevicesService NetworkDevicesService { get; }
        IServersService ServersService { get; }
        ISoftwareService SoftwareService { get; }
        IInterviewService InterviewService { get; }
        IAttendanceServices AttendanceService { get; }
        ITeamCampaignServices TeamCampaignServices { get; }
        ITeamLeaderService TeamLeaderService { get; }
        IQualitySheetService QualitySheetService { get; }
        IPayslipService PayslipService { get; }
        IRankService RankService { get; }
        IDashboardService DashboardService { get; }
    }
}
