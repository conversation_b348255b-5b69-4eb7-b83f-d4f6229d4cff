﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.Services.Models.InterviewModels
{
    public class InterviewDashboardModel
    {
        [JsonProperty("applicationCode")]
        public int Id { get; set; }
        public string Name { get; set; }
        public string Mobile { get; set; }
        public string PresentAddress { get; set; }
        public DateTime Date { get; set; }
        public bool Status { get; set; }
    }
}
