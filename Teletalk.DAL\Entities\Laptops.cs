﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.DAL.Entities
{
    public class Laptops
    {
        public Guid Id { get; set; }
        public string DeviceName { get; set; }
        public string Model { get; set; }
        public string AssetID { get; set; }
        public string SerialNumber { get; set; }
        public string AssetLocation { get; set; }
        public string ApplicationBusinessSpecificUse { get; set; }
        public string Owner { get; set; }
        public string Custodian { get; set; }
        public string User { get; set; }
        public string IPAddress { get; set; }
        public string CPU { get; set; }
        public string RAM { get; set; }
        public string HDD { get; set; }
        public string BackUpMethod { get; set; }
        public string BackUpFrequency { get; set; }
        public string AntiVirusLastUpdated { get; set; }
        public string WhetherUsedOutOfPremises { get; set; }
        public string ConfidentialityRequirementsForDataStored { get; set; }
        public string IntegrityRequirementsForDataStored { get; set; }
        public string AvailabilityRequirementsForDataStored { get; set; }
    }
}
