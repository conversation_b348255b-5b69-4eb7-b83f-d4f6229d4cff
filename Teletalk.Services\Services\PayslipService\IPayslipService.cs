﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.PayslipModels;

namespace Teletalk.Services.Services.PayslipService
{
    public interface IPayslipService 
    {
        Task<MiddlewareObject<bool>> CreatePayslip(AddPayslipReq req);
        Task<MiddlewareObject<List<GetListPayslipRes>>> GetAllPayslip();
        Task<MiddlewareObject<GetPayslipByIdRes>> GetPayslipById(Guid id);
        Task<MiddlewareObject<bool>> UpdatePayslip(Guid id, UpdatePayslipReq req);
    }
}
