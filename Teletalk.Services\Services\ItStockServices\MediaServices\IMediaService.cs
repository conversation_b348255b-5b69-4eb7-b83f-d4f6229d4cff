﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.MediaModels;

namespace Teletalk.Services.Services.ItStockServices.MediaServices
{
    public interface IMediaService
    {
        Task<MiddlewareObject<List<MediaModel>>> GetAllMedia();
        Task<MiddlewareObject<bool>> AddMedia(AddMediaRequest request);
        Task<MiddlewareObject<bool>> DeleteMedia(Guid id);
        Task<MiddlewareObject<bool>> UpdateMedia(Guid id, MediaForUpdateModel dto);
        Task<MiddlewareObject<MediaModel>> GetMediaById(Guid id);
    }
}
