﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.Services.Common
{
    public class MiddlewareObject<T>
    {
        public MiddlewareObject()
        {
            IsSuccess = true;
            StatusCode = AppStatics.Success_Status_Code;
        }
        public MiddlewareObject(T data)
        {
            Data = data;
            IsSuccess = true;
            StatusCode = AppStatics.Success_Status_Code;
        }
        public MiddlewareObject(Exception error) : this(error, error.Message)
        {
        }
        public MiddlewareObject(Exception error, string message)
        {
            IsSuccess = error == null;
            Message = message;
            Error = error?.Message;
        }
        public MiddlewareObject(T data, string message)
        {
            Data = data;
            IsSuccess = true;
            Message = message;
            StatusCode = AppStatics.Success_Status_Code;
        }
        
        public T Data { get; internal set; }
        [JsonIgnore]
        public string Error { get; internal set; }
        public string Message { get; internal set; }
        public bool IsSuccess { get; internal set; }
        public int StatusCode { get; internal set; }
        public int Count { get; internal set; }

        public static implicit operator MiddlewareObject<T>(Type v)
        {
            throw new NotImplementedException();
        }
    }
}

