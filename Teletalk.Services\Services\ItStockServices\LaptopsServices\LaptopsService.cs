﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.LaptopsModels;

namespace Teletalk.Services.Services.ItStockServices.LaptopsServices
{
    public class LaptopsService : ILaptopsService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public LaptopsService(IRepositoryManager repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> AddLaptop(AddLaptopRequest request)
        {
            try
            {
                var newLaptop = _mapper.Map<Laptops>(request);
                _repository.LaptopsRepo.Create(newLaptop);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<LaptopsModel>>> GetAllLaptops()
        {
            try
            {
                var query = await _repository.LaptopsRepo.FindAll(false).ToListAsync();
                var result = _mapper.Map<List<LaptopsModel>>(query);
                return new MiddlewareObject<List<LaptopsModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<LaptopsModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> DeleteLaptop(Guid id)
        {
            try
            {
                var query = await _repository.LaptopsRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                _repository.LaptopsRepo.Delete(query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Deleted Successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> UpdateLaptop(Guid id, LaptopforupdateModel dto)
        {
            try
            {
                var query = await _repository.LaptopsRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                var UpdatedLaptop = _mapper.Map(dto, query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Updated successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<LaptopsModel>> GetLaptopById(Guid id)
        {
            try
            {
                var query = await _repository.LaptopsRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                if (query == null)
                {
                    return new MiddlewareObject<LaptopsModel>(error: null, message: "something went wrong") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
                }
                var result = _mapper.Map<LaptopsModel>(query);
                return new MiddlewareObject<LaptopsModel>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<LaptopsModel>(error: null, message: "something went wrong") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
