﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.DAL.Entities
{
    public class Payslip
    {
        public Guid Id { get; set; }
        public string EmployeeName { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public int? Target { get; set; }
        public int? NumberOfSales { get; set; }
        public decimal Percentage { get; set; }
        public decimal Salary { get; set; }
        public decimal Commisions { get; set; }
        public decimal Bonus { get; set; }
        public decimal TotalSalary { get; set; }
        public string Deduction { get; set; }
        public string OtherDeduction { get; set; }
        public string UserId { get; set; }
        public ApplicationUsers User { get; set; }

        public bool IsDelete { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
    }
}
