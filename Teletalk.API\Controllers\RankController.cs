﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Models.RankModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RankController : ControllerBase
    {
        private readonly IServiceManager serviceManager;
        public RankController(IServiceManager serviceManager)
        {
            this.serviceManager = serviceManager;
        }
        //[HttpPost("get-agents-top-rank")]
        //public async Task<IActionResult> GetAgentsTopRank()
        //{
        //    var result = await serviceManager.RankService.GetAgentsTopRank();
        //    return Ok(result);
        //}
        [HttpPost("get-agents-top-rank")]
        public async Task<IActionResult> GetTopAgentByDailySales(Guid CampaignId)
        {
            var result = await serviceManager.RankService.GetTopAgentByDailySales();
            return Ok(result);
        }
    }
}
