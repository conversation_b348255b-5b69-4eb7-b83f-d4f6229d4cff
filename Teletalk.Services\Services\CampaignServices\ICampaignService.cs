﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.Services.Common;
using Teletalk.Services.Models.CampaignModels;

namespace Teletalk.Services.Services.CampaignServices
{
    public interface ICampaignService
    {
        Task<MiddlewareObject<List<CampaignModel>>> GetAllCampaigns();

        Task<MiddlewareObject<bool>> AddCampaign(AddCampaignRequest request);

        Task<MiddlewareObject<List<CampaignModel>>> GetActiveCampaign();

        Task<MiddlewareObject<List<FormDataTypesModel>>> GetDataTypes();

        Task<MiddlewareObject<bool>> DeleteCampaign(Guid id);

        Task<MiddlewareObject<bool>> UpdateCampaign(Guid id, CampaginForUpdateModel dto);
        Task<MiddlewareObject<GetCampaignByIdRes>> GetCampaignById(Guid id); 
    }
}
