﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.AgentSubmissionDTOS;
using Teletalk.Services.Models.CampaignModels;

namespace Teletalk.Services.Services.AgentSubmissionService
{
    public interface IAgentSubmissionServices
    {
        Task<MiddlewareObject<string>> AddAgentSubmissionService(string submissionForm, string userId, string campaignId);
        Task<MiddlewareObject<CampaginForUpdateModel>> GetSubmissionFormByAgentId(string UserId);
        Task<MiddlewareObject<List<AgentSubmissionDTO>>> GetAgentSubmissionsByUserIdAsync(string UserId);
        Task<MiddlewareObject<List<Submittedform>>> GetSubmittedFormById(string submissionId);
        Task<MiddlewareObject<List<GetAgentsbyTeamLeaderRes>>> GetAgentsByTeamNumber(GetAgentsbyTeamLeaderReq req);
        Task<MiddlewareObject<List<GetAgentSubmissionByStatusRes>>> GetAgentSubmissionByStatus(GetAgentSubmissionByStatusReq req);




        Task<MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>> GetAgentSubmissionsByCampaignId(GetAgentSubmissionsByCampaignIdReq req);
        Task<MiddlewareObject<GetAgentSubmissionGraphRes>> GetAgentSubmissionGraph(GetAgentSubmissionGraphReq req);
    }
}
