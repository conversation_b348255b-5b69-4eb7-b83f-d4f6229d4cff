﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.InterviewModels;

namespace Teletalk.Services.Services.InterviewSubmissionService
{
    public interface IInterviewService
    {
        Task<MiddlewareObject<bool>> CreateInterviewSubmission(AddInterviewReqModel interviewModel);
        Task<MiddlewareObject<List<InterviewDashboardModel>>> GetInterviewDashboard(GetInterviewPagination req);
        Task<MiddlewareObject<bool>> UpdateInterviewSubmission(int id, UpdateInterviewReq req);
        Task<MiddlewareObject<GetInterviewByIdResModel>> GetInterviewById(int id);
    }
}
