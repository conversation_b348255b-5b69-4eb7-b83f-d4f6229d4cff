﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.InterviewModels;

namespace Teletalk.Services.Services.InterviewSubmissionService
{
    public class InterviewService : IInterviewService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public InterviewService(IRepositoryManager repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> CreateInterviewSubmission(AddInterviewReqModel interviewModel)
        {
            var InterviewEntity = _mapper.Map<InterviewSubmission>(interviewModel);
            _repository.InterviewSubmissionRepository.Create(InterviewEntity);
            await _repository.SaveAsync();
            return new MiddlewareObject<bool>(true, "Success");
        }
        public async Task<MiddlewareObject<List<InterviewDashboardModel>>> GetInterviewDashboard(GetInterviewPagination req)
        {
            var counts = _repository.InterviewSubmissionRepository.FindAll(false).Count();

            var interviewEntities = await _repository.InterviewSubmissionRepository.FindAll(false)
                .Skip((req.PageNumber - 1) * req.PageSize)
                .Take(req.PageSize)
                .ToListAsync();

            var result = _mapper.Map<List<InterviewDashboardModel>>(interviewEntities);

            return new MiddlewareObject<List<InterviewDashboardModel>>(result) { Count = counts };
        }
        public async Task<MiddlewareObject<bool>> UpdateInterviewSubmission(int id, UpdateInterviewReq req)
        {
            var interviewEntity = await _repository.InterviewSubmissionRepository.FindByCondition(x => x.Id == id, false).FirstOrDefaultAsync();
            if (interviewEntity == null)
            {
                return new MiddlewareObject<bool>(false, "Interview not found");
            }
            interviewEntity.Status = req.Status;
            _repository.InterviewSubmissionRepository.Update(interviewEntity);
            await _repository.SaveAsync();
            return new MiddlewareObject<bool>(true, "Success");
        }
        public async Task<MiddlewareObject<GetInterviewByIdResModel>> GetInterviewById(int id)
        {
            var interviewEntity = await _repository.InterviewSubmissionRepository.FindByCondition(x => x.Id == id, false).FirstOrDefaultAsync();
            if (interviewEntity == null)
            {
                return new MiddlewareObject<GetInterviewByIdResModel>((GetInterviewByIdResModel)null, "Interview not found");
            }
            var result = _mapper.Map<GetInterviewByIdResModel>(interviewEntity);
            return new MiddlewareObject<GetInterviewByIdResModel>(result);
        }
    }
}
