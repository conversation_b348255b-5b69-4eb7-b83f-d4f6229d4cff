﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.AttendanceModels;
using Teletalk.Services.Models.AuthDTOS;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AttendanceController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public AttendanceController(IServiceManager serviceManager) 
            => _serviceManager = serviceManager;

        [HttpPost("list")]
        public async Task<MiddlewareObject<List<GetAllAttendanceResponse>>> list([FromQuery] GetAllAttenanceRequest parms)
        {
            var result = await _serviceManager.AttendanceService.GetAllAttendance(parms);
            return result;
        }
        [HttpPut("{id}")]
        public async Task<MiddlewareObject<bool>> update(string id, [FromBody] UpdateAttendanceModel model)
        {
            var result = await _serviceManager.AttendanceService.UpdateAttendance(id, model);
            return result;
        }
    }
}
