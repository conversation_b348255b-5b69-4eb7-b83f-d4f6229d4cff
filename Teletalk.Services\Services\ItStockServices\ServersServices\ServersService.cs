﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.NetworkDevicesModels;
using Teletalk.Services.Models.ItStockModels.ServersModels;

namespace Teletalk.Services.Services.ItStockServices.ServersServices
{
    public class ServersService : IServersService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public ServersService(IRepositoryManager repository,IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> AddServer(AddServerRequest request)
        {
            try
            {
                var newServer = _mapper.Map<Servers>(request);
                _repository.ServersRepo.Create(newServer);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<List<ServerModel>>> GetAllServers()
        {
            try
            {
                var query = await _repository.ServersRepo.FindAll(false).ToListAsync();
                var result = _mapper.Map<List<ServerModel>>(query);
                return new MiddlewareObject<List<ServerModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<ServerModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }


        public async Task<MiddlewareObject<bool>> DeleteServer(Guid id)
        {
            try
            {
                var query = await _repository.ServersRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                _repository.ServersRepo.Delete(query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Deleted Successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }


        public async Task<MiddlewareObject<bool>> UpdateServer(Guid id, ServerForUpdateModel dto)
        {
            try
            {
                var query = await _repository.ServersRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                var UpdatedServer = _mapper.Map(dto, query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Updated successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<ServerModel>> GetServerById(Guid id)
        {
            try
            {
                var query = await _repository.ServersRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                if (query == null)
                {
                    return new MiddlewareObject<ServerModel>(error: null, message: "something went wrong") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
                }
                var result = _mapper.Map<ServerModel>(query);
                return new MiddlewareObject<ServerModel>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<ServerModel>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
