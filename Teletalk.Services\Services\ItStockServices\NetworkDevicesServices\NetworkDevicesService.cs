﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.NetworkDevicesModels;

namespace Teletalk.Services.Services.ItStockServices.NetworkDevicesServices
{
    public class NetworkDevicesService : INetworkDevicesService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public NetworkDevicesService(IRepositoryManager repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> AddNetworkDevice(AddNetworkDevicesRequest request)
        {
            try
            {
                var newNetworkDevice = _mapper.Map<NetworkDevices>(request);
                _repository.NetworkDevicesRepo.Create(newNetworkDevice);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<NetworkDevicesModel>>> GetAllNetworkDevices()
        {
            try
            {
                var query = await _repository.NetworkDevicesRepo.FindAll(false).ToListAsync();
                var result = _mapper.Map<List<NetworkDevicesModel>>(query);
                return new MiddlewareObject<List<NetworkDevicesModel>>(result, "Success") { Count = result.Count() };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<NetworkDevicesModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> DeleteNetworkDevice(Guid id)
        {
            try
            {
                var query = await _repository.NetworkDevicesRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                _repository.NetworkDevicesRepo.Delete(query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Deleted Successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> UpdateNetworkDevice(Guid id, NetworkDevicesForUpdateModel dto)
        {
            try
            {
                var query = await _repository.NetworkDevicesRepo.FindByCondition(x => x.Id.Equals(id), true).FirstOrDefaultAsync();
                var UpdatedNetwork = _mapper.Map(dto, query);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true, "Updated successfully");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<NetworkDevicesModel>> GetNetworkDeviceById(Guid id)
        {
            try
            {
                var query = await _repository.NetworkDevicesRepo.FindByCondition(x => x.Id.Equals(id), false).FirstOrDefaultAsync();
                var result = _mapper.Map<NetworkDevicesModel>(query);
                return new MiddlewareObject<NetworkDevicesModel>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<NetworkDevicesModel>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
