﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.Services.Models.AuthDTOS
{
    public class UpdateUserModel
    {
        // we need to add the prop for knowing if it is a (new - edit).
        // if "Id" sends with value that means (edit) if else (new).
        public string FullName { get; set; }
        public string Title { get; set; }
        public string PhoneNumber { get; set; }
        public string IdNumber { get; set; }
        //public string AssignTeamType { get; set; }
        public int? TeamNumber { get; set; }
        public string Branch { get; set; }
        public string Deviceid { get; set; }
        public string Gender { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        //public RolesModel Role { get; set; }
        //public CampaignModel Campaign { get; set; }
        //public List<PermissionsModel> permissions { get; set; }

        public string RoleId { get; set; }
        public string CampaignId { get; set; }
        public List<string> permissions { get; set; }
    }
}
