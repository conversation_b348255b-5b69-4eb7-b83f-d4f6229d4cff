﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Context;
using Teletalk.DAL.Infrastructure.GenreicRepo;
using Teletalk.DAL.Lookups;

namespace Teletalk.DAL.Infrastructure.Repos.FormDatatypeRepository
{
    public class FormDataTypesRepository : RepositoryBase<FormDataType>, IFormDatatypesRepository
    {
        public FormDataTypesRepository(ApplicationDbContext context) : base(context)
        {
            
        }
    }
}
