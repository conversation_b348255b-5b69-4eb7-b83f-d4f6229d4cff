using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using NLog.LayoutRenderers.Wrappers;
using System.Linq;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Models.Enums;
using Teletalk.Services.Common;
using Teletalk.Services.Extensions;
using Teletalk.Services.Models.DashboardModels;

namespace Teletalk.Services.Services.DashboardService
{
    public class DashboardService : IDashboardService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly UserManager<ApplicationUsers> _UserManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public DashboardService(IRepositoryManager repository, IMapper mapper, UserManager<ApplicationUsers> UserManager, IHttpContextAccessor httpContextAccessor)
        {
            _repository = repository;
            _mapper = mapper;
            _UserManager = UserManager;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<MiddlewareObject<List<ChartDataPointModel>>> GetDaliyTarget(GetDailyTargetReqModel req)
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();

                if (role == "Admin")
                {
                    if (req.CampaignId != null)
                    {
                        var AgentSubmission = _repository.AgentSubmission.FindByCondition(x => x.CampaignId == req.CampaignId &&
                          x.CreatedDate >= req.FromDate &&
                          x.CreatedDate <= req.ToDate, false);

                        if (req.TeamNumber != null)
                        {
                            AgentSubmission = AgentSubmission.Where(x => x.TeamNumber == req.TeamNumber);
                        }
                        if (req.AgentId != null)
                        {
                            AgentSubmission = AgentSubmission.Where(x => x.AgentId == req.AgentId);
                        }

                        var result = AgentSubmission.GroupBy(x => x.AgentName)
                            .Select(g => new ChartDataPointModel
                            {
                                x = g.Key.ToString(),
                                y = g.Count()
                            }).ToList();

                        return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
                    }
                    else
                    {
                        return new MiddlewareObject<List<ChartDataPointModel>>((List<ChartDataPointModel>)null, "Please enter campaign Id");
                    }
                }
                else if (role == "TeamLeader")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();

                    var userEntity = await _UserManager.Users.Where(u => u.Id == userId).FirstOrDefaultAsync();

                    var AgentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId == userEntity.CampaignId &&
                        x.CreatedDate <= req.ToDate &&
                        x.CreatedDate >= req.FromDate, false)
                       .ToList();

                    var result = AgentSubmissions.GroupBy(x => x.AgentName)
                          .Select(g => new ChartDataPointModel
                          {
                              x = g.Key.ToString(),
                              y = g.Count()
                          }).ToList();

                    return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
                }
                else if (role == "Agent")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();

                    var userEntity = await _UserManager.Users.Where(u => u.Id == userId).FirstOrDefaultAsync();

                    var AgentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId == userEntity.CampaignId &&
                        x.AgentId == req.AgentId &&
                        x.CreatedDate <= req.ToDate &&
                        x.CreatedDate >= req.FromDate, false)
                       .ToList();
                    
                    var result = AgentSubmissions.GroupBy(x => x.AgentName)
                        .Select(g => new ChartDataPointModel
                        {
                            x = g.Key.ToString(),
                            y = g.Count()
                        }).ToList();

                    return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
                }
                
                return new MiddlewareObject<List<ChartDataPointModel>>((List<ChartDataPointModel>)null, "Please enter campaign Id");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<ChartDataPointModel>>((List<ChartDataPointModel>)null, "Please enter campaign Id");
            }
        }

        public async Task<MiddlewareObject<List<ChartDataPointModel>>> GetMonthlyTarget(GetMonthlyTargetReqModel req)
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();
                if (role == "Admin")
                {
                    var AgentSubmission = _repository.AgentSubmission.FindByCondition(x => x.CampaignId == req.CampaignId &&
                          x.CreatedDate >= req.FromDate &&
                          x.CreatedDate <= req.ToDate, false);

                    if (req.TeamNumber != null)
                    {
                        AgentSubmission = AgentSubmission.Where(x => x.TeamNumber == req.TeamNumber);
                    }
                    if (req.AgentId != null)
                    {
                        AgentSubmission = AgentSubmission.Where(x => x.AgentId == req.AgentId);
                    }

                    var result = AgentSubmission.GroupBy(x => x.AgentName)
                        .Select(g => new ChartDataPointModel
                        {
                            x = g.Key.ToString(),
                            y = g.Count()
                        }).ToList();

                    return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
                }
                else if (role == "TeamLeader")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();

                    var userEntity = await _UserManager.Users.Where(u => u.Id == userId).FirstOrDefaultAsync();

                    var AgentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId == userEntity.CampaignId &&
                        x.CreatedDate <= req.ToDate &&
                        x.CreatedDate >= req.FromDate, false)
                       .ToList();

                    var result = AgentSubmissions.GroupBy(x => x.AgentName)
                          .Select(g => new ChartDataPointModel
                          {
                              x = g.Key.ToString(),
                              y = g.Count()
                          }).ToList();

                    return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
                }
                else if (role == "Agent")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();

                    var userEntity = await _UserManager.Users.Where(u => u.Id == userId).FirstOrDefaultAsync();

                    var AgentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId == userEntity.CampaignId &&
                        x.AgentId == req.AgentId &&
                        x.CreatedDate <= req.ToDate &&
                        x.CreatedDate >= req.FromDate, false)
                       .ToList();

                    var result = AgentSubmissions.GroupBy(x => x.AgentName)
                        .Select(g => new ChartDataPointModel
                        {
                            x = g.Key.ToString(),
                            y = g.Count()
                        }).ToList();

                    return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
                }
                return new MiddlewareObject<List<ChartDataPointModel>>((List<ChartDataPointModel>)null, "Please enter campaign Id");
            }
            catch (Exception)
            {
                return new MiddlewareObject<List<ChartDataPointModel>>((List<ChartDataPointModel>)null, "Please enter campaign Id");
            }
        }

        public async Task<MiddlewareObject<GetPercentageOfAgentRankResModel>> GetPercentageOfRank(GetPercentageOfAgentRankReqModel req)
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();

                int CountDailyAgentSubmission;
                int CountMonthlyAgentSubmission;
                decimal DailyTarget;
                decimal MonthlyTarget;

                if (role == "Admin" && req.CampaignId != null)
                {
                    if (req.TeamNumber == null && req.AgentId == null)
                    {
                        var resultList = new GetPercentageOfAgentRankResModel();

                        var agents = await _UserManager.GetUsersInRoleAsync("Agent");
                        var agentIds = agents.Select(x => x.Id).ToList();

                        // Filter submissions
                        var filteredDailySubmissions = _repository.AgentSubmission
                            .FindByCondition(x => x.CampaignId == req.CampaignId &&
                                                  agentIds.Contains(x.AgentId) &&
                                                  x.CreatedDate >= req.DailyFromDate &&
                                                  x.CreatedDate <= req.DailyToDate &&
                                                  x.status == Status.Approved, false)
                            .ToList();

                        var filteredMonthlySubmissions = _repository.AgentSubmission
                            .FindByCondition(x => x.CampaignId == req.CampaignId &&
                                                  agentIds.Contains(x.AgentId) &&
                                                  x.CreatedDate >= req.MonthlyFromDate &&
                                                  x.CreatedDate <= req.MonthlyToDate &&
                                                  x.status == Status.Approved, false)
                            .ToList();

                        var teamLeaders = await _UserManager.GetUsersInRoleAsync("TeamLeader");
                        var teamLeaderWithCampaignIds = teamLeaders
                            .Where(x => x.CampaignId == req.CampaignId)
                            .Select(u => u.Id)
                            .ToList();

                        var latestSubmissionsDaily = _repository.TeamLeaderSubmissionRepo
                             .FindByCondition(x => teamLeaderWithCampaignIds.Contains(x.UserId) &&
                                                  x.CreatedDate >= req.DailyFromDate &&
                                                  x.CreatedDate <= req.DailyToDate, false)
                             .GroupBy(x => x.UserId)
                             .Select(g => g.OrderByDescending(x => x.CreatedDate).FirstOrDefault())
                             .ToList();
                        var dailyTarget = latestSubmissionsDaily.Sum(x => x?.DailyTarget ?? 0);

                        var latestSubmissionsMonths = _repository.TeamLeaderSubmissionRepo
                             .FindByCondition(x => teamLeaderWithCampaignIds.Contains(x.UserId) &&
                                                  x.CreatedDate >= req.MonthlyFromDate &&
                                                  x.CreatedDate <= req.MonthlyToDate, false)
                             .GroupBy(x => x.UserId)
                             .Select(g => g.OrderByDescending(x => x.CreatedDate).FirstOrDefault())
                             .ToList();
                        var monthlyTarget = latestSubmissionsMonths.Sum(x => x?.MonthlyTarget ?? 0);

                        // Avoid divide by zero
                        dailyTarget = dailyTarget == 0 ? 1 : dailyTarget;
                        monthlyTarget = monthlyTarget == 0 ? 1 : monthlyTarget;

                        // Group submissions by agent and calculate percentages
                        var agentGroupedDaily = filteredDailySubmissions
                            .GroupBy(x => x.AgentId)
                            .ToDictionary(g => g.Key, g => g.Count());

                        var agentGroupedMonthly = filteredMonthlySubmissions
                            .GroupBy(x => x.AgentId)
                            .ToDictionary(g => g.Key, g => g.Count());

                        var totalDailyCount = filteredDailySubmissions.Count;
                        var totalMonthlyCount = filteredMonthlySubmissions.Count;

                        var percentageOfDaily = (totalDailyCount / (decimal)dailyTarget) * 100;
                        var percentageOfMonthly = (totalMonthlyCount / (decimal)monthlyTarget) * 100;

                        var result = new GetPercentageOfAgentRankResModel
                        {
                            DailyTargetPercentage = Math.Round(percentageOfDaily, 2),
                            MonthlyTargetPercentage = Math.Round(percentageOfMonthly, 2),
                            DailyRank = null,
                            MonthlyRank = null
                        };

                        return new MiddlewareObject<GetPercentageOfAgentRankResModel>(result);
                    }
                    else if (req.TeamNumber != null && req.AgentId == null)
                    {
                        var resultList = new GetPercentageOfAgentRankResModel();

                        var agents = await _UserManager.GetUsersInRoleAsync("Agent");
                        var agentIds = agents.Select(x => x.Id).ToList();

                        // Filter submissions
                        var filteredDailySubmissions = _repository.AgentSubmission
                            .FindByCondition(x => x.CampaignId == req.CampaignId &&
                                                x.TeamNumber == req.TeamNumber &&
                                                agentIds.Contains(x.AgentId) &&
                                                x.CreatedDate >= req.DailyFromDate &&
                                                x.CreatedDate <= req.DailyToDate &&
                                                x.status == Status.Approved, false)
                            .ToList();

                        var filteredMonthlySubmissions = _repository.AgentSubmission
                            .FindByCondition(x => x.CampaignId == req.CampaignId &&
                                                x.TeamNumber == req.TeamNumber &&
                                                agentIds.Contains(x.AgentId) &&
                                                x.CreatedDate >= req.MonthlyFromDate &&
                                                x.CreatedDate <= req.MonthlyToDate &&
                                                x.status == Status.Approved, false)
                            .ToList();

                        var teamLeaders = await _UserManager.GetUsersInRoleAsync("TeamLeader");
                        var TeamLeaderUserEntity = teamLeaders
                            .Where(x => x.CampaignId == req.CampaignId
                            && x.TeamNumber == req.TeamNumber)
                            .FirstOrDefault();

                        //var UserId = _httpContextAccessor.HttpContext?.User.GetUserId();
                        //var UserEntity = await _UserManager.Users
                        //    .Where(u => u.CampaignId == req.CampaignId && u.TeamNumber == req.TeamNumber)
                        //    .FirstOrDefaultAsync();

                        var dailyTarget = _repository.TeamLeaderSubmissionRepo
                            .FindByCondition(x => x.UserId == TeamLeaderUserEntity.Id &&
                                                  x.CreatedDate >= req.DailyFromDate &&
                                                  x.CreatedDate <= req.DailyToDate, false)
                            .OrderByDescending(x => x.CreatedDate)
                            .FirstOrDefault()?
                            .DailyTarget ?? 0;

                        var monthlyTarget = _repository.TeamLeaderSubmissionRepo
                            .FindByCondition(x => x.UserId == TeamLeaderUserEntity.Id &&
                                                  x.CreatedDate >= req.MonthlyFromDate &&
                                                  x.CreatedDate <= req.MonthlyToDate, false)
                            .OrderByDescending(x => x.CreatedDate)
                            .FirstOrDefault()?
                            .MonthlyTarget ?? 0;

                        // Avoid divide by zero
                        dailyTarget = dailyTarget == 0 ? 1 : dailyTarget;
                        monthlyTarget = monthlyTarget == 0 ? 1 : monthlyTarget;

                        // Group submissions by agent and calculate percentages
                        var agentGroupedDaily = filteredDailySubmissions
                            .GroupBy(x => x.AgentId)
                            .ToDictionary(g => g.Key, g => g.Count());

                        var agentGroupedMonthly = filteredMonthlySubmissions
                            .GroupBy(x => x.AgentId)
                            .ToDictionary(g => g.Key, g => g.Count());

                        var totalDailyCount = filteredDailySubmissions.Count;
                        var totalMonthlyCount = filteredMonthlySubmissions.Count;

                        var percentageOfDaily = (totalDailyCount / (decimal)dailyTarget) * 100;
                        var percentageOfMonthly = (totalMonthlyCount / (decimal)monthlyTarget) * 100;

                        var result = new GetPercentageOfAgentRankResModel
                        {
                            DailyTargetPercentage = Math.Round(percentageOfDaily, 2),
                            MonthlyTargetPercentage = Math.Round(percentageOfMonthly, 2),
                            DailyRank = null,
                            MonthlyRank = null
                        };

                        return new MiddlewareObject<GetPercentageOfAgentRankResModel>(resultList);
                    }
                    else if (req.TeamNumber != null && req.AgentId != null)
                    {
                        var resultList = new GetPercentageOfAgentRankResModel();

                        var AgentEntity = await _UserManager.Users
                            .Where(u => u.Id == req.AgentId)
                            .FirstOrDefaultAsync();

                        // Filter submissions
                        var filteredDailySubmissions = _repository.AgentSubmission
                            .FindByCondition(x => x.CampaignId == req.CampaignId &&
                                                x.TeamNumber == req.TeamNumber &&
                                                x.AgentId == req.AgentId &&
                                                x.CreatedDate >= req.DailyFromDate &&
                                                x.CreatedDate <= req.DailyToDate &&
                                                x.status == Status.Approved, false)
                            .ToList();

                        var filteredMonthlySubmissions = _repository.AgentSubmission
                            .FindByCondition(x => x.CampaignId == req.CampaignId &&
                                                x.TeamNumber == req.TeamNumber &&
                                                x.AgentId == req.AgentId &&
                                                x.CreatedDate >= req.MonthlyFromDate &&
                                                x.CreatedDate <= req.MonthlyToDate &&
                                                x.status == Status.Approved, false)
                            .ToList();


                        var teamLeaders = await _UserManager.GetUsersInRoleAsync("TeamLeader");
                        var TeamLeaderUserEntity = teamLeaders
                            .Where(x => x.CampaignId == req.CampaignId
                            && x.TeamNumber == req.TeamNumber)
                            .FirstOrDefault();

                        var dailyTarget = _repository.TeamLeaderSubmissionRepo
                            .FindByCondition(x => x.UserId == TeamLeaderUserEntity.Id &&
                                                  x.CreatedDate >= req.DailyFromDate &&
                                                  x.CreatedDate <= req.DailyToDate, false)
                            .OrderByDescending(x => x.CreatedDate)
                            .FirstOrDefault()?
                            .DailyTarget ?? 0;

                        var monthlyTarget = _repository.TeamLeaderSubmissionRepo
                            .FindByCondition(x => x.UserId == TeamLeaderUserEntity.Id &&
                                                  x.CreatedDate >= req.MonthlyFromDate &&
                                                  x.CreatedDate <= req.MonthlyToDate, false)
                            .OrderByDescending(x => x.CreatedDate)
                            .FirstOrDefault()?
                            .MonthlyTarget ?? 0;

                        // Avoid divide by zero
                        dailyTarget = dailyTarget == 0 ? 1 : dailyTarget;
                        monthlyTarget = monthlyTarget == 0 ? 1 : monthlyTarget;

                        var dailyCount = filteredDailySubmissions.Count;
                        var monthlyCount = filteredMonthlySubmissions.Count;

                        var percentageOfDaily = (dailyCount / (decimal)dailyTarget) * 100;
                        var percentageOfMonthly = (monthlyCount / (decimal)monthlyTarget) * 100;

                        // Assign result values
                        resultList.DailyTargetPercentage = Math.Round(percentageOfDaily, 2);
                        resultList.MonthlyTargetPercentage = Math.Round(percentageOfMonthly, 2);

                        return new MiddlewareObject<GetPercentageOfAgentRankResModel>(resultList);
                    }
                }
                else if (role == "TeamLeader")
                {
                    var resultList = new GetPercentageOfAgentRankResModel();

                    var TeamLeaderId = _httpContextAccessor.HttpContext?.User.GetUserId();
                    var UserEntity = await _UserManager.Users
                        .Where(u => u.Id == TeamLeaderId)
                        .FirstOrDefaultAsync();

                    // Filter submissions
                    var filteredDailySubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.TeamNumber == UserEntity.TeamNumber &&
                                              x.CreatedDate >= req.DailyFromDate &&
                                              x.CreatedDate <= req.DailyToDate &&
                                              x.status == Status.Approved, false)
                        .ToList();

                    var filteredMonthlySubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.TeamNumber == UserEntity.TeamNumber &&
                                              x.CreatedDate >= req.MonthlyFromDate &&
                                              x.CreatedDate <= req.MonthlyToDate &&
                                              x.status == Status.Approved, false)
                        .ToList();

                    var firstDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    var lastDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month,
                        DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

                    var dailyTarget = _repository.TeamLeaderSubmissionRepo
                        .FindByCondition(x => x.UserId == UserEntity.Id &&
                        x.CreatedDate >= firstDayOfMonth &&
                        x.CreatedDate <= lastDayOfMonth, false)
                        .FirstOrDefault().DailyTarget;

                    var monthlyTarget = _repository.TeamLeaderSubmissionRepo
                        .FindByCondition(x => x.UserId == UserEntity.Id &&
                        x.CreatedDate >= firstDayOfMonth &&
                        x.CreatedDate <= lastDayOfMonth, false)
                        .FirstOrDefault().MonthlyTarget;

                    // Avoid divide by zero
                    dailyTarget = dailyTarget == 0 ? 1 : dailyTarget;
                    monthlyTarget = monthlyTarget == 0 ? 1 : monthlyTarget;

                    // Group submissions by agent and calculate percentages
                    var agentGroupedDaily = filteredDailySubmissions
                        .GroupBy(x => x.AgentId)
                        .ToDictionary(g => g.Key, g => g.Count());

                    var agentGroupedMonthly = filteredMonthlySubmissions
                        .GroupBy(x => x.AgentId)
                        .ToDictionary(g => g.Key, g => g.Count());

                    // Build stats
                    var agentStats = agentGroupedDaily.Keys
                        .Union(agentGroupedMonthly.Keys)
                        .Select(agentId => new
                        {
                            AgentId = agentId,
                            DailyCount = agentGroupedDaily.ContainsKey(agentId) ? agentGroupedDaily[agentId] : 0,
                            MonthlyCount = agentGroupedMonthly.ContainsKey(agentId) ? agentGroupedMonthly[agentId] : 0
                        })
                        .ToList();

                    // Rank agents
                    var dailyRanked = agentStats
                        .OrderByDescending(x => x.DailyCount)
                        .Select((x, index) => new { x.AgentId, Rank = index + 1 })
                        .ToDictionary(x => x.AgentId, x => x.Rank);

                    var monthlyRanked = agentStats
                        .OrderByDescending(x => x.MonthlyCount)
                        .Select((x, index) => new { x.AgentId, Rank = index + 1 })
                        .ToDictionary(x => x.AgentId, x => x.Rank);

                    // Find the specific agent to report on
                    var agentIdToReturn = req.AgentId;
                    if (string.IsNullOrEmpty(agentIdToReturn))
                        return new MiddlewareObject<GetPercentageOfAgentRankResModel>((GetPercentageOfAgentRankResModel)null, "AgentId is required") { IsSuccess = false };

                    var agentDailyCount = agentGroupedDaily.ContainsKey(agentIdToReturn) ? agentGroupedDaily[agentIdToReturn] : 0;
                    var agentMonthlyCount = agentGroupedMonthly.ContainsKey(agentIdToReturn) ? agentGroupedMonthly[agentIdToReturn] : 0;

                    var dailyPercentage = (agentDailyCount / (decimal)dailyTarget) * 100;
                    var monthlyPercentage = (agentMonthlyCount / (decimal)monthlyTarget) * 100;

                    // Fill the model
                    resultList.DailyTargetPercentage = Math.Round(dailyPercentage, 2);
                    resultList.MonthlyTargetPercentage = Math.Round(monthlyPercentage, 2);
                    resultList.DailyRank = dailyRanked.ContainsKey(agentIdToReturn) ? dailyRanked[agentIdToReturn] : (int?)null;
                    resultList.MonthlyRank = monthlyRanked.ContainsKey(agentIdToReturn) ? monthlyRanked[agentIdToReturn] : (int?)null;

                    return new MiddlewareObject<GetPercentageOfAgentRankResModel>(resultList);
                }
                else if (role == "Agent")
                {
                    var resultList = new GetPercentageOfAgentRankResModel();

                    var AgentId = _httpContextAccessor.HttpContext?.User.GetUserId();
                    var UserEntity = await _UserManager.Users
                        .Where(u => u.Id == AgentId)
                        .FirstOrDefaultAsync();

                    // get the team leader of the agent 
                    var TeamLeaderEntity = _UserManager.GetUsersInRoleAsync("TeamLeader")
                        .Result.Where(u => u.TeamNumber == UserEntity.TeamNumber).FirstOrDefault();

                    // Filter submissions
                    var filteredDailySubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.AgentId == UserEntity.Id &&
                                              x.CreatedDate >= req.DailyFromDate &&
                                              x.CreatedDate <= req.DailyToDate &&
                                              x.status == Status.Approved, false)
                        .ToList();

                    var filteredMonthlySubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.AgentId == UserEntity.Id &&
                                              x.CreatedDate >= req.MonthlyFromDate &&
                                              x.CreatedDate <= req.MonthlyToDate &&
                                              x.status == Status.Approved, false)
                        .ToList();

                    var firstDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                    var lastDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month,
                        DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

                    var dailyTarget = _repository.TeamLeaderSubmissionRepo
                       .FindByCondition(x => x.UserId == TeamLeaderEntity.Id &&
                           x.CreatedDate >= req.MonthlyFromDate &&
                           x.CreatedDate <= req.MonthlyToDate, false)
                       .OrderByDescending(x => x.CreatedDate)
                       .FirstOrDefault().DailyTarget;

                    var monthlyTarget = _repository.TeamLeaderSubmissionRepo
                        .FindByCondition(x => x.UserId == UserEntity.Id &&
                        x.CreatedDate >= req.MonthlyFromDate &&
                        x.CreatedDate <= req.MonthlyToDate, false)
                        .OrderByDescending(x => x.CreatedDate)
                        .FirstOrDefault().MonthlyTarget;

                    // Avoid divide by zero
                    dailyTarget = dailyTarget == 0 ? 1 : dailyTarget;
                    monthlyTarget = monthlyTarget == 0 ? 1 : monthlyTarget;

                    // Group submissions by agent and calculate percentages
                    var agentGroupedDaily = filteredDailySubmissions
                        .GroupBy(x => x.AgentId)
                        .ToDictionary(g => g.Key, g => g.Count());

                    var agentGroupedMonthly = filteredMonthlySubmissions
                        .GroupBy(x => x.AgentId)
                        .ToDictionary(g => g.Key, g => g.Count());

                    // Build stats
                    var agentStats = agentGroupedDaily.Keys
                        .Union(agentGroupedMonthly.Keys)
                        .Select(agentId => new
                        {
                            AgentId = agentId,
                            DailyCount = agentGroupedDaily.ContainsKey(agentId) ? agentGroupedDaily[agentId] : 0,
                            MonthlyCount = agentGroupedMonthly.ContainsKey(agentId) ? agentGroupedMonthly[agentId] : 0
                        })
                        .ToList();

                    // Rank agents
                    var dailyRanked = agentStats
                        .OrderByDescending(x => x.DailyCount)
                        .Select((x, index) => new { x.AgentId, Rank = index + 1 })
                        .ToDictionary(x => x.AgentId, x => x.Rank);

                    var monthlyRanked = agentStats
                        .OrderByDescending(x => x.MonthlyCount)
                        .Select((x, index) => new { x.AgentId, Rank = index + 1 })
                        .ToDictionary(x => x.AgentId, x => x.Rank);

                    // Find the specific agent to report on
                    var agentIdToReturn = req.AgentId;
                    if (string.IsNullOrEmpty(agentIdToReturn))
                        return new MiddlewareObject<GetPercentageOfAgentRankResModel>((GetPercentageOfAgentRankResModel)null, "AgentId is required") { IsSuccess = false };

                    var agentDailyCount = agentGroupedDaily.ContainsKey(agentIdToReturn) ? agentGroupedDaily[agentIdToReturn] : 0;
                    var agentMonthlyCount = agentGroupedMonthly.ContainsKey(agentIdToReturn) ? agentGroupedMonthly[agentIdToReturn] : 0;

                    var dailyPercentage = (agentDailyCount / (decimal)dailyTarget) * 100;
                    var monthlyPercentage = (agentMonthlyCount / (decimal)monthlyTarget) * 100;

                    // Fill the model
                    resultList.DailyTargetPercentage = Math.Round(dailyPercentage, 2);
                    resultList.MonthlyTargetPercentage = Math.Round(monthlyPercentage, 2);
                    resultList.DailyRank = dailyRanked.ContainsKey(agentIdToReturn) ? dailyRanked[agentIdToReturn] : (int?)null;
                    resultList.MonthlyRank = monthlyRanked.ContainsKey(agentIdToReturn) ? monthlyRanked[agentIdToReturn] : (int?)null;

                    return new MiddlewareObject<GetPercentageOfAgentRankResModel>(resultList);
                }
                return new MiddlewareObject<GetPercentageOfAgentRankResModel>((GetPercentageOfAgentRankResModel)null);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<GetPercentageOfAgentRankResModel>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<List<GetStatisticsCardsResModel>>> GetStatisticsCards()
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();
                var userId = _httpContextAccessor.HttpContext?.User.GetUserId();
                var today = DateTime.Today;
                var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

                var users = new List<ApplicationUsers>();
                if (role == "Admin")
                {
                    users = await _UserManager.Users.ToListAsync();
                }
                else if (role == "TeamLeader")
                {
                    var teamLeader = await _UserManager.Users.FirstOrDefaultAsync(u => u.Id == userId);
                    users = await _UserManager.Users.Where(u => u.TeamNumber == teamLeader.TeamNumber).ToListAsync();
                }
                else // Agent
                {
                    var agent = await _UserManager.Users.FirstOrDefaultAsync(u => u.Id == userId);
                    users.Add(agent);
                }

                var result = new List<GetStatisticsCardsResModel>();

                foreach (var user in users)
                {
                    var teamLeaderSubmission = await _repository.TeamLeaderSubmissionRepo
                        .FindByCondition(t => t.UserId == user.Id, false)
                        .FirstOrDefaultAsync();

                    var dailyTarget = teamLeaderSubmission?.DailyTarget ?? 0;
                    var monthlyTarget = teamLeaderSubmission?.MonthlyTarget ?? 0;

                    var submissionsToday = await _repository.AgentSubmission
                        .FindByCondition(a => a.AgentId == user.Id && a.CreatedDate.Date == today, false)
                        .CountAsync();

                    var submissionsThisMonth = await _repository.AgentSubmission
                        .FindByCondition(a => a.AgentId == user.Id && a.CreatedDate >= firstDayOfMonth && a.CreatedDate <= lastDayOfMonth, false)
                        .CountAsync();

                    var teamAgents = await _UserManager.Users
                        .Where(u => u.TeamNumber == user.TeamNumber)
                        .ToListAsync();

                    var agentRanks = new List<KeyValuePair<string, int>>();
                    foreach (var agent in teamAgents)
                    {
                        var agentSubmissionsThisMonth = await _repository.AgentSubmission
                            .FindByCondition(a => a.AgentId == agent.Id && a.CreatedDate >= firstDayOfMonth && a.CreatedDate <= lastDayOfMonth, false)
                            .CountAsync();
                        agentRanks.Add(new KeyValuePair<string, int>(agent.Id, agentSubmissionsThisMonth));
                    }

                    var rankedAgents = agentRanks.OrderByDescending(kvp => kvp.Value).ToList();
                    var agentRank = rankedAgents.FindIndex(kvp => kvp.Key == user.Id) + 1;

                    result.Add(new GetStatisticsCardsResModel
                    {
                        AgentName = user.UserName,
                        SubmissionsToday = submissionsToday,
                        DailyTarget = (int)dailyTarget,
                        DailyTargetPercentage = dailyTarget > 0 ? (decimal)submissionsToday / dailyTarget * 100 : 0,
                        SubmissionsThisMonth = submissionsThisMonth,
                        MonthlyTarget = (int)monthlyTarget,
                        MonthlyTargetPercentage = monthlyTarget > 0 ? (decimal)submissionsThisMonth / monthlyTarget * 100 : 0,
                        AgentRank = agentRank
                    });
                }

                return new MiddlewareObject<List<GetStatisticsCardsResModel>>(result);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<GetStatisticsCardsResModel>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        #region New Dashboard Methods

        public async Task<MiddlewareObject<DashboardOverviewModel>> GetDashboardOverview(GetDashboardOverviewReqModel req)
        {
            try
            {
                var dailySalesChart = await GetDailySalesChart(req);
                var monthlySalesChart = await GetMonthlySalesChart(req);
                var dashboardCards = await GetDashboardCards(req);

                if (!dailySalesChart.IsSuccess || !monthlySalesChart.IsSuccess || !dashboardCards.IsSuccess)
                {
                    return new MiddlewareObject<DashboardOverviewModel>(error: null, message: "Failed to retrieve dashboard data")
                    { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
                }

                var result = new DashboardOverviewModel
                {
                    DailySalesChart = dailySalesChart.Data,
                    MonthlySalesChart = monthlySalesChart.Data,
                    Cards = dashboardCards.Data
                };

                return new MiddlewareObject<DashboardOverviewModel>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<DashboardOverviewModel>(error: null, message: "Something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<List<ChartDataPointModel>>> GetDailySalesChart(GetDashboardDailyReqModel req) 
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();
                var userId = _httpContextAccessor.HttpContext?.User.GetUserId();
                var targetDate = req.DailyDate ?? DateTime.Today;

                var agentSubmissions = new List<AgentSubmission>();

                if (role == "Admin")
                {
                    var query = _repository.AgentSubmission
                        .FindByCondition(x => x.CreatedDate.Date == targetDate.Date && 
                        x.status == Status.Approved, false);

                    if (req.CampaignId.HasValue)
                        query = query.Where(x => x.CampaignId == req.CampaignId.Value);

                    if (req.TeamNumber.HasValue)
                        query = query.Where(x => x.TeamNumber == req.TeamNumber.Value);

                    if (!string.IsNullOrEmpty(req.AgentId))
                        query = query.Where(x => x.AgentId == req.AgentId);

                    agentSubmissions = query.ToList();
                }
                else if (role == "TeamLeader")
                {
                    var userEntity = await _UserManager.Users.FirstOrDefaultAsync(u => u.Id == userId);
                    agentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId == userEntity.CampaignId &&
                                             x.CreatedDate.Date == targetDate.Date &&
                                            x.status == Status.Approved, false)
                        .ToList();
                }
                else if (role == "Agent")
                {
                    agentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.AgentId == userId &&
                                             x.CreatedDate.Date == targetDate.Date &&
                                            x.status == Status.Approved, false)
                        .ToList();
                }

                //var MonthlyTarget = await GetMonthlyTarget(req, targetDate);

                var result = agentSubmissions
                    .GroupBy(x => x.AgentName)
                    .Select(g => new ChartDataPointModel
                    {
                        x = g.Key,
                        y = g.Count(),
                    })
                    .OrderByDescending(x => x.y)
                    .ToList();

                return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<ChartDataPointModel>>(error: null, message: "Something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<List<ChartDataPointModel>>> GetMonthlySalesChart(GetDashboardMonthlyReqModel req)
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();
                var userId = _httpContextAccessor.HttpContext?.User.GetUserId();
                var fromDate = req.MonthlyFromDate ?? new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var toDate = req.MonthlyToDate ?? new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

                var agentSubmissions = new List<AgentSubmission>();

                if (role == "Admin")
                {
                    var query = _repository.AgentSubmission
                        .FindByCondition(x => x.CreatedDate >= fromDate && x.CreatedDate <= toDate && x.status == Status.Approved, false);

                    if (req.CampaignId.HasValue)
                        query = query.Where(x => x.CampaignId == req.CampaignId.Value);

                    if (req.TeamNumber.HasValue)
                        query = query.Where(x => x.TeamNumber == req.TeamNumber.Value);

                    if (!string.IsNullOrEmpty(req.AgentId))
                        query = query.Where(x => x.AgentId == req.AgentId);

                    agentSubmissions = query.ToList();
                }
                else if (role == "TeamLeader")
                {
                    var userEntity = await _UserManager.Users.FirstOrDefaultAsync(u => u.Id == userId);
                    agentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId == userEntity.CampaignId &&
                                             x.CreatedDate >= fromDate &&
                                             x.CreatedDate <= toDate &&
                                            x.status == Status.Approved, false)
                        .ToList();
                }
                else if (role == "Agent")
                {
                    agentSubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.AgentId == userId &&
                                             x.CreatedDate >= fromDate &&
                                             x.CreatedDate <= toDate &&
                                            x.status == Status.Approved, false)
                        .ToList();
                }

                var result = agentSubmissions
                    .GroupBy(x => x.AgentName)
                    .Select(g => new ChartDataPointModel
                    {
                        x = g.Key,
                        y = g.Count()
                    })
                    .OrderByDescending(x => x.y)
                    .ToList();

                return new MiddlewareObject<List<ChartDataPointModel>>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<ChartDataPointModel>>(error: null, message: "Something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        public async Task<MiddlewareObject<DashboardCardsModel>> GetDashboardCards(GetDashboardOverviewReqModel req)
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();
                var userId = _httpContextAccessor.HttpContext?.User.GetUserId();
                var targetDate = req.DailyDate ?? DateTime.Today;
                var fromDate = req.MonthlyFromDate ?? new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var toDate = req.MonthlyToDate ?? new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month));

                var result = new DashboardCardsModel();

                if (role == "Admin")
                {
                    // For Admin, show overall statistics
                    var allAgents = await _UserManager.GetUsersInRoleAsync("Agent");

                    if (req.CampaignId.HasValue)
                    {
                        allAgents = allAgents.Where(u => u.CampaignId == req.CampaignId.Value).ToList();
                    }

                    if (req.TeamNumber.HasValue)
                    {
                        allAgents = allAgents.Where(u => u.TeamNumber == req.TeamNumber.Value).ToList();
                    }

                    // Calculate daily statistics
                    var dailySubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CreatedDate.Date == targetDate.Date, false);

                    var monthlySubmissions = _repository.AgentSubmission
                        .FindByCondition(x => x.CreatedDate >= fromDate && x.CreatedDate <= toDate, false);

                    if (req.CampaignId.HasValue)
                    {
                        dailySubmissions = dailySubmissions.Where(x => x.CampaignId == req.CampaignId.Value);
                        monthlySubmissions = monthlySubmissions.Where(x => x.CampaignId == req.CampaignId.Value);
                    }

                    if (req.TeamNumber.HasValue)
                    {
                        dailySubmissions = dailySubmissions.Where(x => x.TeamNumber == req.TeamNumber.Value);
                        monthlySubmissions = monthlySubmissions.Where(x => x.TeamNumber == req.TeamNumber.Value);
                    }

                    var dailySubmissionsList = dailySubmissions.ToList();
                    var monthlySubmissionsList = monthlySubmissions.ToList();

                    // Get targets from team leader submissions
                    var teamLeaderSubmissions = await _repository.TeamLeaderSubmissionRepo
                        .FindByCondition(x => x.CreatedDate >= fromDate && x.CreatedDate <= toDate, false)
                        .ToListAsync();

                    var totalDailyTarget = teamLeaderSubmissions.Sum(x => x.DailyTarget);
                    var totalMonthlyTarget = teamLeaderSubmissions.Sum(x => x.MonthlyTarget);

                    result.DailyTargetPercentage = totalDailyTarget > 0 ? (decimal)dailySubmissionsList.Count / totalDailyTarget * 100 : 0;
                    result.MonthlyTargetPercentage = totalMonthlyTarget > 0 ? (decimal)monthlySubmissionsList.Count / totalMonthlyTarget * 100 : 0;

                    // Calculate ranks (Admin gets rank 1 by default or based on overall performance)
                    result.DailySalesRank = 1;
                    result.MonthlySalesRank = 1;
                }
                else if (role == "TeamLeader" || role == "Agent")
                {
                    var userEntity = await _UserManager.Users.FirstOrDefaultAsync(u => u.Id == userId);

                    // Get user's submissions
                    var userDailySubmissions = await _repository.AgentSubmission
                        .FindByCondition(x => x.AgentId == userId && x.CreatedDate.Date == targetDate.Date, false)
                        .CountAsync();

                    var userMonthlySubmissions = await _repository.AgentSubmission
                        .FindByCondition(x => x.AgentId == userId && x.CreatedDate >= fromDate && x.CreatedDate <= toDate, false)
                        .CountAsync();

                    // Get user's targets
                    var teamLeaderSubmission = await _repository.TeamLeaderSubmissionRepo
                        .FindByCondition(x => x.UserId == userId, false)
                        .FirstOrDefaultAsync();

                    if (role == "Agent" && teamLeaderSubmission == null)
                    {
                        // Get team leader's targets for agent
                        var teamLeader = await _UserManager.GetUsersInRoleAsync("TeamLeader");
                        var userTeamLeader = teamLeader.FirstOrDefault(tl => tl.TeamNumber == userEntity.TeamNumber);

                        if (userTeamLeader != null)
                        {
                            teamLeaderSubmission = await _repository.TeamLeaderSubmissionRepo
                                .FindByCondition(x => x.UserId == userTeamLeader.Id, false)
                                .FirstOrDefaultAsync();
                        }
                    }

                    var dailyTarget = teamLeaderSubmission?.DailyTarget ?? 0;
                    var monthlyTarget = teamLeaderSubmission?.MonthlyTarget ?? 0;

                    result.DailyTargetPercentage = dailyTarget > 0 ? (decimal)userDailySubmissions / dailyTarget * 100 : 0;
                    result.MonthlyTargetPercentage = monthlyTarget > 0 ? (decimal)userMonthlySubmissions / monthlyTarget * 100 : 0;

                    // Calculate ranks within team or campaign
                    var teamAgents = await _UserManager.Users
                        .Where(u => u.TeamNumber == userEntity.TeamNumber)
                        .ToListAsync();

                    // Daily rank calculation
                    var dailyRanks = new List<(string AgentId, int Count)>();
                    foreach (var agent in teamAgents)
                    {
                        var count = await _repository.AgentSubmission
                            .FindByCondition(x => x.AgentId == agent.Id && x.CreatedDate.Date == targetDate.Date, false)
                            .CountAsync();
                        dailyRanks.Add((agent.Id, count));
                    }

                    var sortedDailyRanks = dailyRanks.OrderByDescending(x => x.Count).ToList();
                    result.DailySalesRank = sortedDailyRanks.FindIndex(x => x.AgentId == userId) + 1;

                    // Monthly rank calculation
                    var monthlyRanks = new List<(string AgentId, int Count)>();
                    foreach (var agent in teamAgents)
                    {
                        var count = await _repository.AgentSubmission
                            .FindByCondition(x => x.AgentId == agent.Id && x.CreatedDate >= fromDate && x.CreatedDate <= toDate, false)
                            .CountAsync();
                        monthlyRanks.Add((agent.Id, count));
                    }

                    var sortedMonthlyRanks = monthlyRanks.OrderByDescending(x => x.Count).ToList();
                    result.MonthlySalesRank = sortedMonthlyRanks.FindIndex(x => x.AgentId == userId) + 1;
                }

                return new MiddlewareObject<DashboardCardsModel>(result, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<DashboardCardsModel>(error: null, message: "Something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        private async Task<int> GetMonthlyTarget(GetDashboardOverviewReqModel req, DateTime targetDate)
        {
            var teamLeaders = await _UserManager.GetUsersInRoleAsync("TeamLeader");

            var teamLeaderWithCampaignIds = teamLeaders
                .Where(x => x.CampaignId == req.CampaignId)
                .Select(u => u.Id)
                .ToList();

            var latestSubmissionsDaily = _repository.TeamLeaderSubmissionRepo
                 .FindByCondition(x => teamLeaderWithCampaignIds.Contains(x.UserId) &&
                                      x.CreatedDate.Month == targetDate.Month, false)
                 .OrderByDescending(x => x.MonthlyTarget)
                 .FirstOrDefault()?.MonthlyTarget ?? 0;

            return latestSubmissionsDaily;
        }
        #endregion
    }
}
