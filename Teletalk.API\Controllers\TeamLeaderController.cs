﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.TeamLeaderModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TeamLeaderController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public TeamLeaderController(IServiceManager serviceManager)
        {
            _serviceManager = serviceManager;
        }
        [Authorize(Roles = "TeamLeader")]
        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddTeamLeaderSubmission(AddTeamLeaderSubmissionModel req)
        {
            var result = await _serviceManager.TeamLeaderService.AddTeamLeaderSubmission(req);
            return result;  
        }
        [HttpPost("change-agent-submission-status")]
        public async Task<IActionResult> ChangeAgentSubmissionStatus(ChangeAgentSubmissionStatusReq req)
        {
            var result = await _serviceManager.TeamLeaderService.ChangeAgentSubmissionStatus(req);
            return Ok(result);
        }
    }
}
