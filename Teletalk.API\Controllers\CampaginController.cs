﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.AgentSubmissionDTOS;
using Teletalk.Services.Models.CampaignModels;
using Teletalk.Services.Models.TeamCampaignModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CampaginController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public CampaginController(IServiceManager serviceManager) => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<IActionResult> AddCampaign([FromBody] AddCampaignRequest request)
        {
            var result = await _serviceManager.CampaignService.AddCampaign(request);
            return Ok(result);
        }

        [HttpGet()]
        public async Task<MiddlewareObject<List<CampaignModel>>> GetActiveCampaign()
        {
            var result = await _serviceManager.CampaignService.GetActiveCampaign();
            return result;
        }

        [HttpDelete("{id}")]
        public async Task<MiddlewareObject<bool>> DeleteCampaign(Guid id)
        {
            var result=await _serviceManager.CampaignService.DeleteCampaign(id);
            return result;
        }

        [HttpPut("{id}")]
        public async Task<MiddlewareObject<bool>> UpdateCampaign(Guid id, [FromBody] CampaginForUpdateModel dto)
        {
            var result = await _serviceManager.CampaignService.UpdateCampaign(id,dto);
            return result;
        }

        [HttpPost("addTeam")]
        public async Task<MiddlewareObject<bool>> AddTeamNumber([FromBody] AddTeamCampaignModel req)
        {
            //var result = await _serviceManager.CampaignService.UpdateTeamNumber(id, req);
            var result = await _serviceManager.TeamCampaignServices.AddTeamCampaign(req);
            return new MiddlewareObject<bool>(true);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> GetCampaignById(Guid id)
        {
            var result = await _serviceManager.CampaignService.GetCampaignById(id);
            return Ok(result);
        }
    }
}
