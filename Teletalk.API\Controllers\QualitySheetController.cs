﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Teletalk.Services.Common;
using Teletalk.Services.Models.CampaignModels;
using Teletalk.Services.Models.QualitySheetModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class QualitySheetController : ControllerBase
    {

        private readonly IServiceManager _serviceManager;
        public QualitySheetController(IServiceManager serviceManager)
        {
            _serviceManager = serviceManager;
        }

        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddQualitySheet([FromBody] AddQualitySheet request)
        {
            var result = await _serviceManager.QualitySheetService.AddQualitySheet(request);
            return result;
        }
        [HttpPost("get-agent-quality-sheet-grid")] // Admin Teamleader quailty and agent 
        public async Task<MiddlewareObject<List<GetAllQualitySheets>>> GetAllQualitySheets([FromQuery] GetQuailtySheetsByAgentId req)
        {
            //// Extract the role from claims
            //var role = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role || c.Type == "role")?.Value;
            var result = await _serviceManager.QualitySheetService.GetAllQualitySheets(req);
            return result;
        }

        [HttpGet("{id}")]
        public async Task<MiddlewareObject<QualitySheetDetails>> GetQualitySheetDetails(Guid id)
        {
            var result = await _serviceManager.QualitySheetService.GetQualitySheetDetails(id);
            return result;
        }

        [HttpPut("{id}")]
        public async Task<MiddlewareObject<bool>> UpdateQualitySheet(Guid id, [FromBody] UpdateQualitySheetModel dto)
        {
            var result = await _serviceManager.QualitySheetService.UpdateQualitySheetAsync(id,dto);   
            return result;
        }
    }
}
