﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.NetworkDevicesModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ITStockNetworkDevicesController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public ITStockNetworkDevicesController(IServiceManager serviceManager) => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddNetworkDevice([FromBody] AddNetworkDevicesRequest request)
        {
            var result = await _serviceManager.NetworkDevicesService.AddNetworkDevice(request);
            return result;
        }
        [HttpGet()]
        public async Task<MiddlewareObject<List<NetworkDevicesModel>>> GetAllNetworkDevices()
        {
            var result = await _serviceManager.NetworkDevicesService.GetAllNetworkDevices();
            return result;
        }

        [HttpDelete()]
        public async Task<MiddlewareObject<bool>> DeleteNetworkDevice(Guid id)
        {
            var result = await _serviceManager.NetworkDevicesService.DeleteNetworkDevice(id);
            return result;
        }
        [HttpPut()]
        public async Task<MiddlewareObject<bool>> UpdateNetworkDevice(Guid id, [FromBody] NetworkDevicesForUpdateModel dto)
        {
            var result = await _serviceManager.NetworkDevicesService.UpdateNetworkDevice(id, dto);
            return result;
        }
        // Get By Id
        [HttpGet("{id}")]
        public async Task<MiddlewareObject<NetworkDevicesModel>> GetNetworkDeviceById(Guid id)
        {
            var result = await _serviceManager.NetworkDevicesService.GetNetworkDeviceById(id);
            return result;
        }
    }
}
