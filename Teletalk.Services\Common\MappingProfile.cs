﻿using AutoMapper;
using Newtonsoft.Json;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Lookups;
using Teletalk.Models.RegisterModel;
using Teletalk.Services.Models.AgentSubmissionDTOS;
using Teletalk.Services.Models.AttendanceModels;
using Teletalk.Services.Models.AuthDTOS;
using Teletalk.Services.Models.CampaignModels;
using Teletalk.Services.Models.InterviewModels;
using Teletalk.Services.Models.ItStockModels.LaptopsModels;
using Teletalk.Services.Models.ItStockModels.MediaModels;
using Teletalk.Services.Models.ItStockModels.NetworkDevicesModels;
using Teletalk.Services.Models.ItStockModels.ServersModels;
using Teletalk.Services.Models.ItStockModels.SoftwareModels;
using Teletalk.Services.Models.PayslipModels;
using Teletalk.Services.Models.QualitySheetModels;
using Teletalk.Services.Models.TeamCampaignModels;
using Teletalk.Services.Models.TeamLeaderModels;

namespace Teletalk.Services.Common
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            #region ApplicationUserMappings
            CreateMap<ApplicationUsers, RegisterModel>().ReverseMap();
            CreateMap<ApplicationUsers, UserResponseModel>().ReverseMap();
            CreateMap<ApplicationUsers, GetAllUsersResponse>().ReverseMap();
            CreateMap<ApplicationUsers, UpdateUserModel>().ReverseMap();
            //CreateMap<AgentSubmission, AgentSubmissionRequest>().ReverseMap();
            CreateMap<Permissions, PermissionsModel>().ReverseMap();
            CreateMap<PermissionRoles, PermissionRolesModel>().ReverseMap();
            CreateMap<ApplicationUsers, GetAllAgentsUserNamesLookup>().ReverseMap();
            #endregion

            #region CampaignMappings
            CreateMap<Campaign, CampaignModel>().ReverseMap();
            //.ForMember(dest => dest.SubmissionField, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<List<SubmissionFormField>>(src.SubmissionForm))).ReverseMap();
            CreateMap<Campaign, AddCampaignRequest>()
                 .ForMember(dest => dest.fields, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<List<SubmissionFormField>>(src.SubmissionForm))).ReverseMap()
                 .ForMember(dest => dest.SubmissionForm, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.fields)));
            CreateMap<FormDataType, FormDataTypesModel>().ReverseMap();
            CreateMap<CampaginForUpdateModel, Campaign>()
                .ForMember(dest => dest.SubmissionForm, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.Fields)))
                .ForMember(dest => dest.ModifiedDate, opt => opt.MapFrom(src => DateTime.Now));
            #endregion

            #region ItStockTablesMappings
            CreateMap<Laptops, LaptopsModel>().ReverseMap();
            CreateMap<Media, MediaModel>().ReverseMap();
            CreateMap<NetworkDevices, NetworkDevicesModel>().ReverseMap();
            CreateMap<Servers, ServerModel>().ReverseMap();
            CreateMap<Software, SoftwareModel>().ReverseMap();

            CreateMap<Laptops, AddLaptopRequest>().ReverseMap();
            CreateMap<Media, AddMediaRequest>().ReverseMap();
            CreateMap<NetworkDevices, AddNetworkDevicesRequest>().ReverseMap();
            CreateMap<Servers, AddServerRequest>().ReverseMap();
            CreateMap<Software, AddSoftwareRequest>().ReverseMap();

            CreateMap<LaptopforupdateModel, Laptops>().ReverseMap();
            CreateMap<MediaForUpdateModel, Media>().ReverseMap();
            CreateMap<NetworkDevicesForUpdateModel, NetworkDevices>().ReverseMap();
            CreateMap<ServerForUpdateModel, Servers>().ReverseMap();
            CreateMap<SoftwareForUpdateModel, Software>().ReverseMap();
            #endregion

            #region Interview
            CreateMap<InterviewSubmission, AddInterviewReqModel>()
                    .ForMember(dest => dest.WorkExperience, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<List<WorkExperience>>(src.WorkExperience)))
                    .ForMember(dest => dest.AnyRecommendation, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<List<AnyRecommendation>>(src.AnyRecommendation)))
                    .ReverseMap()
                    .ForMember(dest => dest.WorkExperience, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.WorkExperience)))
                    .ForMember(dest => dest.AnyRecommendation, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.AnyRecommendation)));
            
            CreateMap<InterviewSubmission, InterviewDashboardModel>().ReverseMap();
            
            CreateMap<InterviewSubmission, GetInterviewByIdResModel>()
                   .ForMember(dest => dest.WorkExperience, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<List<GetByIdWorkExperience>>(src.WorkExperience)))
                   .ForMember(dest => dest.AnyRecommendation, opt => opt.MapFrom(src => JsonConvert.DeserializeObject<List<GetByIdAnyRecommendation>>(src.AnyRecommendation)))
                   .ReverseMap()
                   .ForMember(dest => dest.WorkExperience, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.WorkExperience)))
                   .ForMember(dest => dest.AnyRecommendation, opt => opt.MapFrom(src => JsonConvert.SerializeObject(src.AnyRecommendation)));
            #endregion

            #region Attendance
            CreateMap<Attendance, GetAllAttendanceResponse>()

                .ForMember(dest => dest.SigninTime, opt => opt.MapFrom(src => src.SigninTime.ToString("h:mm tt")))
                .ForMember(dest => dest.SignOutTime, opt => opt.MapFrom(src => src.SignOutTime == DateTime.MinValue ? "N/A" : src.SignOutTime.ToString("h:mm tt")))
                .ForMember(dest => dest.date, opt => opt.MapFrom(src => src.CreatedDate.ToString("yyyy-MM-dd")))
                .ReverseMap();

            CreateMap<TeamNumberCampaign, AddTeamCampaignModel>().ReverseMap();
            CreateMap<TeamLeaderSubmission, AddTeamLeaderSubmissionModel>().ReverseMap();
            #endregion

            #region QualitySheet
            CreateMap<QualitySheet, GetAllQualitySheets>()
           .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Agent.UserName)).ReverseMap();


            CreateMap<QualitySheet, QualitySheetDetails>()
                .ForMember(dest => dest.SalesAgentUserName, opt => opt.MapFrom(src => src.Agent.UserName)).ReverseMap();

            CreateMap<AddQualitySheet, QualitySheet>().ReverseMap();

            CreateMap<UpdateQualitySheetModel, QualitySheet>().ReverseMap();

            CreateMap<string, QualityRatedAgentsLookup>()
            .ConvertUsing(username => new QualityRatedAgentsLookup { UserName = username }); // this for making only username to be mapped, as it returned from the query as a string using select
            #endregion

            #region AgentSubmission
            CreateMap<AgentSubmission, AgentSubmissionDTO>().ReverseMap();
            #endregion
            
            CreateMap<Payslip, AddPayslipReq>().ReverseMap();
            CreateMap<Payslip, GetListPayslipRes>().ReverseMap();
            CreateMap<Payslip, GetPayslipByIdRes>().ReverseMap();
            CreateMap<Payslip, UpdatePayslipReq>().ReverseMap();
            CreateMap<Campaign, GetCampaignByIdRes>().ReverseMap();


            CreateMap<AgentSubmission, GetAgentSubmissionsByCampaignIdRes>().ReverseMap();
        }
    }
}