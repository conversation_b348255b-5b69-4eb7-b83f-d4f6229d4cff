﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Models.RegisterModel;
using Teletalk.Services.Common;
using Teletalk.Services.Common.Helpers;
using Teletalk.Services.Extensions;
using Teletalk.Services.Models.AuthDTOS;
using Teletalk.Services.Models.LookupsModels;
using Teletalk.Services.Models.RegisterModel;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.Services.Services.Register
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<ApplicationUsers> _UserManager;
        private readonly IMapper _mapper;
        private readonly JWT _JWT;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IRepositoryManager _repositoryManager;
        private readonly IServiceManager _serviceManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public AuthService(UserManager<ApplicationUsers> userManager,
            IMapper mapper, IOptions<JWT> jWT, RoleManager<IdentityRole> roleManager,
            IRepositoryManager repositoryManager,IServiceManager serviceManager,
            IHttpContextAccessor httpContextAccessor)
        {
            _serviceManager = serviceManager;
            _UserManager = userManager;
            _mapper = mapper;
            _JWT = jWT.Value;
            _roleManager = roleManager;
            _repositoryManager = repositoryManager;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<MiddlewareObject<string>> RegisterAsync(RegisterModel registerModel)
        {
            
            if (await _UserManager.FindByNameAsync(registerModel.UserName) != null)
                return new MiddlewareObject<string>(error: null, "UserName is already registered") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            
            // Get users with "TeamLeader" role
            var usersInRole = await _UserManager.GetUsersInRoleAsync("TeamLeader");

            // Parse the CampaignId string safely
            if (Guid.TryParse(registerModel.CampaignId, out Guid parsedCampaignId))
            {
                // Check if any team leader has the same CampaignId and TeamNumber
                var isAssigned = usersInRole
                    .Any(x => x.CampaignId == parsedCampaignId && x.TeamNumber == registerModel.TeamNumber);

                // use isAssigned as needed
                if (isAssigned)
                    return new MiddlewareObject<string>(error: null, $"Team {registerModel.TeamNumber} already have a TeamLeader in the Campaign")
                    { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }

            var newUser = _mapper.Map<ApplicationUsers>(registerModel);
            newUser.Id = Guid.NewGuid().ToString(); // Ensure the Id is set

            var result = await _UserManager.CreateAsync(newUser, registerModel.Password);
            if (!result.Succeeded)
            {
                var errors = string.Join(",", result.Errors.Select(e => e.Description));
                return new MiddlewareObject<string>(error: null, errors) { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }

            var role = await _roleManager.FindByIdAsync(registerModel.RoleId);
            await _UserManager.AddToRoleAsync(newUser, role.Name);

            var permissionsRole = new List<PermissionRolesModel>();

            foreach (var r in registerModel.permissions)
            {
                permissionsRole.Add(new PermissionRolesModel
                {
                    UserID = newUser.Id,
                    RoleId = role?.Id,
                    PermissionId = r,
                });
            }

            var permissionRoles = _mapper.Map<List<PermissionRoles>>(permissionsRole);
            _repositoryManager.permissionRoleRepository.CreateRange(permissionRoles);
            await _repositoryManager.SaveAsync();

            try
            {
                var newPayslip = new Payslip()
                {
                    EmployeeName = newUser.FullName,
                    DateFrom = AppStatics.FIRST_DAY_OF_MONTH,
                    DateTo = AppStatics.LAST_DAY_OF_MONTH,
                    Target = 0,
                    NumberOfSales = 0,
                    Percentage = 0,
                    Salary = 0,
                    Commisions = 0,
                    Bonus = 0,
                    TotalSalary = 0,
                    Deduction = null,
                    OtherDeduction = null,
                    UserId = newUser.Id,
                    IsDelete = false
                };
                _repositoryManager.PaySlipRepository.Create(newPayslip);
                await _repositoryManager.SaveAsync();
            }
            catch (Exception ex)
            {

            }

            if (role.Name == "TeamLeader" || role.Name == "Agent")
            {
                newUser.TeamNumber = registerModel.TeamNumber;
                await _UserManager.UpdateAsync(newUser);
            }
            return new MiddlewareObject<string>(data: null, message: "User Registered Successfully");
        }
        public async Task<MiddlewareObject<AuthModel>> GetTokenLoginAsync(LoginModel tokenRequestModel)
        {
            var authmodel = new AuthModel();
            var user = await _UserManager.FindByNameAsync(tokenRequestModel.UserName);

            if (user is null || !await _UserManager.CheckPasswordAsync(user, tokenRequestModel.Password))
            {
                return new MiddlewareObject<AuthModel>(error: null, "Your Password or User Name incorrect") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }

            var userPermissions = await _repositoryManager.permissionRoleRepository
                .FindByCondition(p => p.UserID == user.Id, false)
                .Include(p => p.Permission)
                .Select(p => new PermissionsModel {
                    Id = p.Permission.Id,
                    PermissionName = p.Permission.PermissionName,
                    Path = p.Permission.Path,
                    Icon = p.Permission.Icon,
                })
                .ToListAsync();

            if (user.isActive == false)
                return new MiddlewareObject<AuthModel>(error: null, "Your Account has been Deactivated, Call your Team Leader") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

            var JWTtoken = await Createtoken(user, userPermissions);
            var rolesList = await _UserManager.GetRolesAsync(user);
            var role = rolesList.FirstOrDefault();

            authmodel.IsAuthenticated = true;
            authmodel.Token = new JwtSecurityTokenHandler().WriteToken(JWTtoken);
            authmodel.UserName = user.UserName;
            authmodel.ExpiresON = JWTtoken.ValidTo;
            authmodel.Role = role;
            authmodel.Permissions = userPermissions;

            if (role != "Admin")
            {
                var attendanceResult = await _serviceManager.AttendanceService.AddAttendanceWithLogin(user);
                if (!attendanceResult.IsSuccess)
                {
                    return new MiddlewareObject<AuthModel>(error: null, message: attendanceResult.Message)
                    {
                        IsSuccess = false,
                        StatusCode = attendanceResult.StatusCode
                    };
                }
            }
            return new MiddlewareObject<AuthModel>(data: authmodel);
        }
        private async Task<JwtSecurityToken> Createtoken(ApplicationUsers user, List<PermissionsModel> permissionsModel)
        {
            var userClaims = await _UserManager.GetClaimsAsync(user);

            //kol user 3ando only one role so we will get one value.
            var role = (await _UserManager.GetRolesAsync(user)).FirstOrDefault();

            // Serialize the permissionsModel list to a JSON string
            var permissionsJson = System.Text.Json.JsonSerializer.Serialize(permissionsModel);

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub,user.UserName),
                new Claim(JwtRegisteredClaimNames.Jti,Guid.NewGuid().ToString()),
                new Claim("uid",user.Id),
                new Claim("Name", user.FullName),
                new Claim(ClaimTypes.Role, role),
                new Claim("Permissions", permissionsJson),
                new Claim("role", role)
            }.Union(userClaims);

            var symmetricsecuritykey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_JWT.Key));

            var signingcredentials = new SigningCredentials(symmetricsecuritykey, SecurityAlgorithms.HmacSha256);
            var jwtsecuritytoken = new JwtSecurityToken(
                issuer: _JWT.Issuer,
                audience: _JWT.Audience,
                claims: claims,
                expires: DateTime.Now.AddDays(_JWT.DurationInDays),
                signingCredentials: signingcredentials);

            return jwtsecuritytoken;
        }
        public async Task<MiddlewareObject<UserResponseModel>> GetUserDetails(string id)
        {
            var user = await _UserManager.Users
                .Where(x => x.Id == id)
                //.Include(x => x.Campaigns)
                .FirstOrDefaultAsync();

            if (user == null)
                return new MiddlewareObject<UserResponseModel>(error: null, "User Not Found") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

            var UsersPermission = await _repositoryManager.permissionRoleRepository
                .FindByCondition(p => p.UserID == user.Id, false)
                .Include(p => p.Permission)
                .Select(p => p.Permission.Id.ToString().ToLower())
                .ToListAsync();

            var Roles = await _UserManager.GetRolesAsync(user);
            var roleId = "";
            
            if(Roles.Any())
            {
                roleId = _roleManager.FindByNameAsync(Roles.FirstOrDefault())?.Result?.Id;
            }
            
            //var result = _mapper.Map<UserResponseModel>(query);
            
            var result = new UserResponseModel
            {
                Id = user.Id,
                IdNumber = user.IdNumber,
                FullName = user.FullName,
                Title = user.Title,
                PhoneNumber = user.PhoneNumber,
                TeamNumber = user.TeamNumber,
                Branch = user.Branch,
                Deviceid = user.Deviceid,
                Gender = user.Gender,
                CampaignId = user.CampaignId,
                //CampaignName = query.Campaigns?.CampaignName,
                UserName = user.UserName,
                RoleId = roleId,
            };

            result.Permissions = UsersPermission;

            return new MiddlewareObject<UserResponseModel>(result, "Success");
        }
        public async Task<MiddlewareObject<bool>> ChangeUserActivation(string id)
        {
            var user = await _UserManager.FindByIdAsync(id);
            if (user == null)
                return new MiddlewareObject<bool>(error: null, "User Not Found") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

            if (user.isActive == false)
            {
                user.isActive = true;
                await _UserManager.UpdateAsync(user);
                return new MiddlewareObject<bool>(true, "Activated Successfully");
            }
            else
            {
                user.isActive = false;
                await _UserManager.UpdateAsync(user);
                return new MiddlewareObject<bool>(true, "Deactivated Successfully");
            }

        }
        public MiddlewareObject<List<PermissionsModel>> GetPermissions()
        {
            var query = _repositoryManager.permissionRepository.FindAll(false).ToList();
            var result = _mapper.Map<List<PermissionsModel>>(query);
            return new MiddlewareObject<List<PermissionsModel>>(result, "Success") { Count = result.Count() };
        }
        public MiddlewareObject<List<GetAllRolesModel>> GetRoles()
        {
            var roles = _roleManager.Roles.ToList();
            var result = roles.Select(r => new GetAllRolesModel
            {
                Id = r.Id,
                RoleName = r.Name
            }).ToList();

            return new MiddlewareObject<List<GetAllRolesModel>>(result, "Success") { Count = result.Count() };
        }
        // oldo
        //public MiddlewareObject<List<int?>> GetTeamsbyCampaignID(Guid id)
        //{
        //    var teams = _UserManager.Users.Where(u => u.CampaignId == id && u.TeamNumber != null)
        //        .Select(u => u.TeamNumber).Distinct().ToList();
        //    return new MiddlewareObject<List<int?>>(teams, "Success") { Count = teams.Count };
        //}
        // newo
        public MiddlewareObject<List<int>> GetTeamsbyCampaignID(Guid id)
        {
            var teams = _repositoryManager.TeamNumberCampaignRepo
                .FindByCondition(u => u.CampaignId == id, false)
                .Select(u => u.TeamNumber).Distinct().ToList();
            return new MiddlewareObject<List<int>>(teams, "Success") { Count = teams.Count };
        }
        public async Task<MiddlewareObject<List<UserNamesLookup>>> GetallUsersName()
        {
            var usernames = _UserManager.Users.AsQueryable();

            var result = await usernames.Select(u => new UserNamesLookup
            {
                id = u.Id,
                UserName = u.UserName
            }).ToListAsync();

            return new MiddlewareObject<List<UserNamesLookup>>(result, "Success") { Count = result.Count() };
        }

        public async Task<MiddlewareObject<List<GetAllAgentsUserNamesLookup>>> GetAllAgentsUserNames(GetAllUserRequest param)
        {
            var usernames =await _UserManager.GetUsersInRoleAsync("Agent");
            var Agents = usernames.AsQueryable().SearchForUsers(param);
            var result = _mapper.Map<List<GetAllAgentsUserNamesLookup>>(Agents);
            return new MiddlewareObject<List<GetAllAgentsUserNamesLookup>>(result, "Success") { Count = result.Count() };
        }
        public async Task<MiddlewareObject<bool>> DeleteUserByid(string id)
        {
            // get the attandance first
            var attendanceEntity = await _repositoryManager.AttendanceRepository
                .FindByCondition(x => x.User.Id == id, trackChanges: true)
                .ToListAsync();
            
            // delete the attandance
            _repositoryManager.AttendanceRepository.DeleteRange(attendanceEntity);

            var UserToDelete = await _UserManager.FindByIdAsync(id);

            if (UserToDelete == null)
                return new MiddlewareObject<bool>(error: null, "User Not Found") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

            var result = await _UserManager.DeleteAsync(UserToDelete);

            return new MiddlewareObject<bool>(true, "User Deleted successfully");
        }
        public async Task<MiddlewareObject<List<GetAllUsersResponse>>> GetAllUsers(GetAllUserRequest parameters)
        {
            try
            {
                var totalCount = _UserManager.Users.Count();

                var users = await _UserManager.Users
                    .SearchForUsers(parameters)
                    .Include(x => x.Campaigns)
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync();
                
                //var result = _mapper.Map<List<GetAllUsersResponse>>(query);
                var result = users.Select(x => new GetAllUsersResponse
                {
                    Id = x.Id,
                    FullName = x.FullName,
                    Title = x.Title,
                    TeamNumber = x.TeamNumber,
                    Branch = x.Branch,
                    Deviceid = x.Deviceid,
                    Gender = x.Gender,
                    Campaignid = x.Campaigns?.Id ?? null,
                    CampaignName = x.Campaigns?.CampaignName ?? null, // fallback string
                    PhoneNumber = x.PhoneNumber,
                    UserName = x.UserName,
                    isActive = x.isActive,
                    RoleId = _UserManager.GetRolesAsync(x).Result.FirstOrDefault()
                }).ToList();

                return new MiddlewareObject<List<GetAllUsersResponse>>(result, "Success") { Count = totalCount };
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<GetAllUsersResponse>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<string>> UpdateUser(string id, UpdateUserModel UpdateUserModel)
        {
            var userToUpdate = await _UserManager.FindByIdAsync(id);
            
            if (userToUpdate == null)
                return new MiddlewareObject<string>(error: null, "User Not Found")
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };

            var roles = await _UserManager.GetRolesAsync(userToUpdate);
            var role = roles.FirstOrDefault();

            // # 2 
            // get the old campaign 
            var oldCampaign = userToUpdate.CampaignId;

            // Get users with "TeamLeader" role
            var usersInRole = await _UserManager.GetUsersInRoleAsync("TeamLeader");

            if (oldCampaign.ToString() != UpdateUserModel.CampaignId)
            {
                // Parse the CampaignId string safely
                if (Guid.TryParse(UpdateUserModel.CampaignId, out Guid parsedCampaignId) && role == "TeamLeader")
                {
                    // Check if any team leader has the same CampaignId and TeamNumber
                    var isAssigned = usersInRole
                        .Any(x => x.CampaignId == parsedCampaignId && x.TeamNumber == UpdateUserModel.TeamNumber);

                    // use isAssigned as needed
                    if (isAssigned)
                        return new MiddlewareObject<string>(error: null, $"Team {UpdateUserModel.TeamNumber} already have a TeamLeader in the Campaign")
                        { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
                }
            }

            _mapper.Map(UpdateUserModel, userToUpdate);
            var updateResult = await _UserManager.UpdateAsync(userToUpdate);

            // old Role
            var Roles = await _UserManager.GetRolesAsync(userToUpdate);
            var roleEntity = _roleManager.FindByNameAsync(Roles.FirstOrDefault())?.Result;
           
            if (await _UserManager.IsInRoleAsync(userToUpdate, roleEntity.Name))
            {
                var removeResult = await _UserManager.RemoveFromRoleAsync(userToUpdate, roleEntity.Name);
            }

            // new role 
            var NewRole = _roleManager.FindByIdAsync(UpdateUserModel.RoleId).Result;
            var addResult = await _UserManager.AddToRoleAsync(userToUpdate, NewRole.Name);

            var PermissionsRoles = _repositoryManager.permissionRoleRepository.FindByCondition(x => x.UserID == id, false).AsEnumerable();
            _repositoryManager.permissionRoleRepository.DeleteRange(PermissionsRoles);

            var permissionsRole = new List<PermissionRolesModel>();

            foreach (var r in UpdateUserModel.permissions)
            {
                permissionsRole.Add(new PermissionRolesModel
                {
                    UserID = userToUpdate.Id,
                    RoleId = NewRole?.Id,
                    PermissionId = r,
                });
            }

            var permissionRoles = _mapper.Map<List<PermissionRoles>>(permissionsRole);
            _repositoryManager.permissionRoleRepository.CreateRange(permissionRoles);
            
            if (!updateResult.Succeeded)
            {
                var errors = string.Join(",", updateResult.Errors.Select(e => e.Description));
                return new MiddlewareObject<string>(error: null, errors)
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }
            await _repositoryManager.SaveAsync();
            return new MiddlewareObject<string>(data: null, message: "User updated successfully");
        }
        public async Task<MiddlewareObject<bool>> logOut()
        {
            try
            {
                var UserId = _httpContextAccessor.HttpContext?.User.GetUserId();

                var attendanceEntity = await _repositoryManager.AttendanceRepository.
                    FindByCondition(x => x.User.Id == UserId, trackChanges: true)
                    .OrderByDescending(x => x.CreatedDate)
                    .FirstOrDefaultAsync();
                
                if (attendanceEntity == null)
                    return new MiddlewareObject<bool>(error: null, "User doesn't exist") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };

                attendanceEntity.SignOutTime = DateTime.Now;
                await _repositoryManager.SaveAsync();
                return new MiddlewareObject<bool>() { IsSuccess = true, StatusCode = AppStatics.Success_Status_Code };
            }
            catch (Exception)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong") { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    } 
}

