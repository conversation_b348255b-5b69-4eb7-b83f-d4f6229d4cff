﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Context;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.GenreicRepo;

namespace Teletalk.DAL.Infrastructure.Repos.PermissionRoleRepository
{
    public class PermissionRoleRepository : RepositoryBase<PermissionRoles>, IPermissionRoleRepository
    {
        public PermissionRoleRepository(ApplicationDbContext context)
           : base(context)
        {
        }
    }
}
