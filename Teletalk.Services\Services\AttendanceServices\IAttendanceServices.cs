﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Entities;
using Teletalk.Services.Common;
using Teletalk.Services.Models.AttendanceModels;

namespace Teletalk.Services.Services.AttendanceServices
{
    public interface IAttendanceServices
    {
        Task<MiddlewareObject<bool>> AddAttendanceWithLogin(ApplicationUsers userName);
        Task<MiddlewareObject<List<GetAllAttendanceResponse>>> GetAllAttendance(GetAllAttenanceRequest req);
        Task<MiddlewareObject<bool>> UpdateAttendance(string id, UpdateAttendanceModel model);
    }
}
