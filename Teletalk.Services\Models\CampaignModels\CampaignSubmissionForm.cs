﻿using System.Text.Json.Serialization;

namespace Teletalk.Services.Models.CampaignModels
{
    public class CampaignSubmissionForm
    {
        [JsonPropertyName("field")]
        public CampaignSubmissionFormField field { get; set; }
        [JsonPropertyName("label")]
        public CampaignLabelForm label { get; set; }
    }
    public class CampaignSubmissionFormField
    {
        [Json<PERSON>ropertyName("name")]
        public string name { get; set; }
        [Json<PERSON>ropertyName("placeholder")]
        public string placeholder { get; set; }
        [JsonPropertyName("validations")]
        public List<CampaignFieldsValidations> validations { get; set; }
        [JsonPropertyName("type")]
        public string type { get; set; }
        [JsonPropertyName("options")]
        public List<CampaignOptions> options { get; set; }
    }
    public class CampaignFieldsValidations
    {
        [JsonPropertyName("type")]
        public string type { get; set; }
        [JsonPropertyName("message")]
        public string message { get; set; }
        [JsonPropertyName("value")]
        public string value { get; set; }
    }
    public class CampaignLabelForm
    {
        [JsonPropertyName("text")]
        public string text { get; set; }
    }
    public class CampaignOptions
    {
        [JsonPropertyName("value")]
        public string value { get; set; }
        [JsonPropertyName("label")]
        public string label { get; set; }
    }
}
