{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "AllowedHosts": "*",
  "ConnectionStrings": {
    //"DefaultConnection": "Data Source=.; Initial Catalog=IS_AutoBackDB; Integrated Security = True; multipleactiveresultsets=True;TrustServerCertificate=true;"
    "DefaultConnection": "Data Source=.; Initial Catalog=NewTeletalkDB; Integrated Security = True; multipleactiveresultsets=True;TrustServerCertificate=True;"
    //"DefaultConnection": "Server=.\\SQLEXPRESS;Database=TeletalkDB;User Id=crmUserr1;Password=******;Integrated Security = True;"
    //"DefaultConnection": "Server=**************\\SQLEXPRESS;Database=TeletalkDB;User Id=crmUserr1;Password=******;Integrated Security = True;"
  },

    "JWT": {
        "Key": "GifY4HDSicI59IaQ8B79Pv8SJIWSdynYncQ79cDNuAM=",
        "Issuer": "secureapi",
        "Audience": "secureapiuser",
        "DurationInDays": 1
           }
}
