﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Extensions;
using Teletalk.Services.Models.TeamLeaderModels;

namespace Teletalk.Services.Services.TeamLeaderServices
{
    public class TeamLeaderService : ITeamLeaderService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public TeamLeaderService(IRepositoryManager repository, IMapper mapper, IHttpContextAccessor httpContextAccessor)
        {
            _repository = repository;
            _mapper = mapper;
            _httpContextAccessor = httpContextAccessor;
        }
        public async Task<MiddlewareObject<bool>> AddTeamLeaderSubmission(AddTeamLeaderSubmissionModel req)
        {
            try
            {
                var UserId = _httpContextAccessor.HttpContext?.User.GetUserId();
                
                var newEntity = _mapper.Map<TeamLeaderSubmission>(req);
                newEntity.MonthlyTarget = req.FullTimeTarget;
                newEntity.DailyTarget = Convert.ToInt16(req.FullTimeTarget / 30);
                newEntity.UserId = UserId;
                
                _repository.TeamLeaderSubmissionRepo.Create(newEntity);
                await _repository.SaveAsync();
                
                return new MiddlewareObject<bool>(true, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
        public async Task<MiddlewareObject<bool>> ChangeAgentSubmissionStatus(ChangeAgentSubmissionStatusReq req)
        {
            try
            {
                var agentSubmission = _repository.AgentSubmission
                    .FindByCondition(x => x.Id == req.AgentSubmissionId, true)
                    .FirstOrDefault();

                agentSubmission.status = req.Status;

                _repository.AgentSubmission.Update(agentSubmission);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
