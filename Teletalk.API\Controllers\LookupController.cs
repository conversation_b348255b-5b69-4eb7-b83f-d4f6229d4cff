﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Models.Enums;
using Teletalk.Services.Common;
using Teletalk.Services.Common.Helpers;
using Teletalk.Services.Models.AuthDTOS;
using Teletalk.Services.Models.CampaignModels;
using Teletalk.Services.Models.QualitySheetModels;
using Teletalk.Services.Services.Register;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LookupController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly IServiceManager _serviceManager;
        public LookupController(IAuthService authService, IServiceManager serviceManager)
        {
            _authService = authService;
            _serviceManager = serviceManager;
        }

        [HttpGet("get-permission")]
        public MiddlewareObject<List<PermissionsModel>> GetPermission()
        {
            var result = _authService.GetPermissions();
            return result;
        }

        [HttpGet("get-roles")]
        public MiddlewareObject<List<GetAllRolesModel>> GetRoles()
        {
            var result = _authService.GetRoles();
            return result;
        }

        [HttpGet("get-teams-by-campagin-id")]
        public MiddlewareObject<List<int>> GetTeamsByCampaginID(Guid id)
        {
            var result = _authService.GetTeamsbyCampaignID(id);
            return result;
        }

        [HttpGet("get-DataTypes")]
        public async Task<MiddlewareObject<List<FormDataTypesModel>>> GetDataTypes()
        {
            var result = await _serviceManager.CampaignService.GetDataTypes();
            return result;
        }

        [HttpGet("get-all-campaigns")]
        public async Task<MiddlewareObject<List<CampaignModel>>> GetAllCampaigns()
        {
            var result = await _serviceManager.CampaignService.GetAllCampaigns();
            return result;
        }

        [HttpPost("get-all-agent-usernames")]
        public async Task<MiddlewareObject<List<GetAllAgentsUserNamesLookup>>> GetAllAgentsUserNames([FromQuery] GetAllUserRequest parms)
        {
            var result = await _authService.GetAllAgentsUserNames(parms);
            return result;
        }
        [HttpGet("get-agents-with-qualitysheets-usernames")]
        public async Task<MiddlewareObject<List<QualityRatedAgentsLookup>>> GetAgentsWithQualitySheetsUserNames()
        {
            var result = await _serviceManager.QualitySheetService.GetAgentsWithQualitySheetsUsernames();
            return result;
        }
        [HttpGet("get-status")]
        public async Task<IActionResult> GetStatus()
        {
            var result = EnumHelper.GetEnumValuesWithText<Status>();
            return Ok(result);
        }
        //[HttpGet("types-dashboard-role-request")]
        //public async Task<IActionResult> TypesDashboardRoleRequest()
        //{
        //    var result = EnumHelper.GetEnumValuesWithText<TypesDashboardRoleRequest>();
        //    return Ok(result);
        //}
    }

}
