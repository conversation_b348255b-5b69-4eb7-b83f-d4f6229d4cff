﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Teletalk.DAL.Entities
{
    public class Campaign
    {
        public Guid Id { get; set; }
        public string CampaignNumber { get; set; }
        public string CampaignName { get; set; }
        public string SubmissionForm { get; set; }
        public bool IsActive { get; set; } = true;
        public ICollection<ApplicationUsers> Users { get; set; }
        public ICollection<TeamNumberCampaign> teamNumberCampaigns { get; set; }
        public bool IsDelete { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
    }
}
