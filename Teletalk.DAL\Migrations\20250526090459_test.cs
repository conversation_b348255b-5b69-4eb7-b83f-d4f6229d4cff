﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teletalk.DAL.Migrations
{
    public partial class test : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("0147457e-a83f-4589-952c-39c3523d4f49"),
                column: "PermissionName",
                value: "Agent's Rank");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("196100ce-7c72-4292-a9bf-a3d419782851"),
                column: "PermissionName",
                value: "Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("24c6642e-3221-4ea4-a811-921315c7fe05"),
                column: "PermissionName",
                value: "Sales Status");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("458384ef-110c-43eb-a8fa-5041f4c8172c"),
                column: "PermissionName",
                value: "Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("4a557104-2b92-4f25-8b33-ea44ed0c905b"),
                column: "PermissionName",
                value: "Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("6441879c-b217-4711-9d8d-24f4f1a8794f"),
                column: "PermissionName",
                value: "IT Stock");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("673390f5-a7ca-4590-bb53-e322c869f283"),
                column: "PermissionName",
                value: "Attendance");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("82ab9a99-31c5-4aac-9b33-090ef178c343"),
                column: "PermissionName",
                value: "Payslip");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("aeb4d2cb-36a3-464b-93a8-21a48dc574a4"),
                column: "PermissionName",
                value: "Target Form");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("b3629e21-f614-425b-b658-efd511615c11"),
                column: "PermissionName",
                value: "Dashboard");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("ba63a002-4d1c-472c-99dc-4816c81153e9"),
                column: "PermissionName",
                value: "Access Control");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("bb034fad-6a61-4984-8435-e17dc6477d3b"),
                column: "PermissionName",
                value: "Interview Application");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("c18fadb1-5c66-4f01-9bcb-5a70c83cd933"),
                column: "PermissionName",
                value: "Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("c6dff7c3-1ea1-40f7-99d2-551ac9fcff00"),
                column: "PermissionName",
                value: "Dashboard");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("d08b33d0-be66-409a-8cf1-d505e8174bb9"),
                column: "PermissionName",
                value: "Target Form");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("dde025e0-84bd-4bac-a782-be0ff0243a47"),
                column: "PermissionName",
                value: "Quality Sheet");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("f7764f21-dfac-465f-a884-4e5649436ea7"),
                column: "PermissionName",
                value: "Campaign");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("0147457e-a83f-4589-952c-39c3523d4f49"),
                column: "PermissionName",
                value: "Admin Agent's Rank");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("196100ce-7c72-4292-a9bf-a3d419782851"),
                column: "PermissionName",
                value: "Quality Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("24c6642e-3221-4ea4-a811-921315c7fe05"),
                column: "PermissionName",
                value: "Team Leader Sales Status");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("458384ef-110c-43eb-a8fa-5041f4c8172c"),
                column: "PermissionName",
                value: "Admin Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("4a557104-2b92-4f25-8b33-ea44ed0c905b"),
                column: "PermissionName",
                value: "Team Leader Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("6441879c-b217-4711-9d8d-24f4f1a8794f"),
                column: "PermissionName",
                value: "Admin IT Stock");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("673390f5-a7ca-4590-bb53-e322c869f283"),
                column: "PermissionName",
                value: "Hr Attendance");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("82ab9a99-31c5-4aac-9b33-090ef178c343"),
                column: "PermissionName",
                value: "Hr Payslip");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("aeb4d2cb-36a3-464b-93a8-21a48dc574a4"),
                column: "PermissionName",
                value: "Team Leader Target Form");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("b3629e21-f614-425b-b658-efd511615c11"),
                column: "PermissionName",
                value: "Team Leader Dashboard");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("ba63a002-4d1c-472c-99dc-4816c81153e9"),
                column: "PermissionName",
                value: "Admin Access Control");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("bb034fad-6a61-4984-8435-e17dc6477d3b"),
                column: "PermissionName",
                value: "Hr Interview Application");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("c18fadb1-5c66-4f01-9bcb-5a70c83cd933"),
                column: "PermissionName",
                value: "Agent Stored Data");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("c6dff7c3-1ea1-40f7-99d2-551ac9fcff00"),
                column: "PermissionName",
                value: "Agent Dashboard");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("d08b33d0-be66-409a-8cf1-d505e8174bb9"),
                column: "PermissionName",
                value: "Agent Target Form");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("dde025e0-84bd-4bac-a782-be0ff0243a47"),
                column: "PermissionName",
                value: "Agent Quality Sheet");

            migrationBuilder.UpdateData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("f7764f21-dfac-465f-a884-4e5649436ea7"),
                column: "PermissionName",
                value: "Admin Campaign");
        }
    }
}
