﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Models.Enums;
using Teletalk.Services.Common;
using Teletalk.Services.Models.PayslipModels;

namespace Teletalk.Services.Services.PayslipService
{
    public class PayslipService : IPayslipService
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly UserManager<ApplicationUsers> _userManager;
        public PayslipService(IRepositoryManager repository, IMapper mapper, UserManager<ApplicationUsers> userManager)
        {
            _repository = repository;
            _mapper = mapper;
            _userManager = userManager;
        }
        public async Task<MiddlewareObject<bool>> CreatePayslip(AddPayslipReq req)
        {
            try
            {
                var entity = _mapper.Map<Payslip>(req);
                _repository.PaySlipRepository.Create(entity);
                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(false, ex.Message);
            }

        }
        public async Task<MiddlewareObject<List<GetListPayslipRes>>> GetAllPayslip()
        {
            try
            {
                var usersWithFilteredPayslips = await _userManager.Users
                    .Include(u => u.Payslips.Where(p =>
                        p.CreatedDate >= AppStatics.FIRST_DAY_OF_MONTH &&
                        p.CreatedDate <= AppStatics.LAST_DAY_OF_MONTH))
                    .ToListAsync();

                var result = usersWithFilteredPayslips.Select(x => new GetListPayslipRes()
                {
                    Id = x.Id,
                    EmployeeName = x.FullName,
                    
                    DateFrom = AppStatics.FIRST_DAY_OF_MONTH,
                    DateTo = AppStatics.LAST_DAY_OF_MONTH,

                    Target = 0, 
                    NumberOfSales = 0, 
                    Percentage = 0, 
                    
                    Salary = x.Payslips.FirstOrDefault()?.Salary ?? 0,
                    Commisions = x.Payslips.FirstOrDefault()?.Commisions ?? 0,
                    Bonus = x.Payslips.FirstOrDefault()?.Bonus ?? 0,

                    Deduction = x.Payslips.FirstOrDefault()?.Deduction ?? "0", // Safely access Deduction
                    OtherDeduction = x.Payslips.FirstOrDefault()?.OtherDeduction ?? "0", // Safely access OtherDeduction
                    TotalSalary = 0
                }).ToList(); 

                //var payslips = _repository.PaySlipRepository.FindAll(false).ToList();
                //var result = _mapper.Map<List<GetListPayslipRes>>(payslips);
                return new MiddlewareObject<List<GetListPayslipRes>>(result);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<GetListPayslipRes>>((List<GetListPayslipRes>)null, "");
            }
        }
        public async Task<MiddlewareObject<GetPayslipByIdRes>> GetPayslipById(Guid id)
        {
            try
            {
                var payslip = _repository.PaySlipRepository.FindByCondition(x => x.Id == id, false).FirstOrDefault();
                if (payslip == null)
                {
                    return new MiddlewareObject<GetPayslipByIdRes>((GetPayslipByIdRes)null, $"No payslip with {id}");
                }
                var result = _mapper.Map<GetPayslipByIdRes>(payslip);
                return new MiddlewareObject<GetPayslipByIdRes>(result);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<GetPayslipByIdRes>((GetPayslipByIdRes)null, ex.Message);
            }
        }
        public async Task<MiddlewareObject<bool>> UpdatePayslip(Guid id, UpdatePayslipReq req)
        {
            try
            {
                var UserEntity = await _userManager.Users.Where(u => u.Id == id.ToString()).FirstOrDefaultAsync();

                var payslip = _repository.PaySlipRepository.FindByCondition(x => x.UserId == UserEntity.Id, true).FirstOrDefault();
                if (payslip == null)
                    return new MiddlewareObject<bool>(false, $"No payslip with {id}");
           
                var entity = _mapper.Map(req, payslip);
                _repository.PaySlipRepository.Update(entity);

                await _repository.SaveAsync();
                return new MiddlewareObject<bool>(true);
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(false, ex.Message);
            }
        }
    }
}
