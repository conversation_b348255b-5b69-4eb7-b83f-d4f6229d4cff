﻿namespace Teletalk.Services.Models.AgentSubmissionDTOS
{
    public class GetAgentSubmissionByStatusRes
    {
        public string Name { get; set; }
        public DateTime Date { get; set; }
        //public List<GetAgentSubmissionByStatusData> AgentSubmissions { get; set; }
        //public AgentSubmissionStatusCounts Counts { get; set; }
    }
    public class GetAgentSubmissionByStatusData
    {
        //public Guid Id { get; set; }
        //public string AgentId { get; set; }
        //public string AgentName { get; set; }
        //public Guid CampaignId { get; set; }
        //public int? TeamNumber { get; set; }
        //public DateTime SubmissionDate { get; set; }
        //public string Status { get; set; }
    }
    public class AgentSubmissionStatusCounts
    {
        //public int TotalPendingStatus { get; set; }
        //public int TotalApprovedStatus { get; set; }
        //public int TotalDeletedStatus { get; set; }
        //public int TotalPostDateStatus { get; set; }
    }
}
