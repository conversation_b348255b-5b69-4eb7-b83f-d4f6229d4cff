﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Teletalk.Models.PaginationModel;
using Teletalk.Models.RegisterModel;
using Teletalk.Services.Common;
using Teletalk.Services.Models.AuthDTOS;
using Teletalk.Services.Models.LookupsModels;
using Teletalk.Services.Models.RegisterModel;

namespace Teletalk.Services.Services.Register
{
    public interface IAuthService
    {
        Task<MiddlewareObject<string>> RegisterAsync(RegisterModel registerModel);
        Task<MiddlewareObject<AuthModel>> GetTokenLoginAsync(LoginModel tokenRequestModel);
        Task<MiddlewareObject<UserResponseModel>> GetUserDetails(string id);
        MiddlewareObject<List<PermissionsModel>> GetPermissions();
        MiddlewareObject<List<GetAllRolesModel>> GetRoles();
        MiddlewareObject<List<int>> GetTeamsbyCampaignID(Guid id);
        Task<MiddlewareObject<List<UserNamesLookup>>> GetallUsersName();
        Task<MiddlewareObject<bool>> ChangeUserActivation(string id);
        Task<MiddlewareObject<bool>> DeleteUserByid(string id);
        Task<MiddlewareObject<List<GetAllUsersResponse>>> GetAllUsers(GetAllUserRequest parameters);
        Task<MiddlewareObject<string>> UpdateUser(string id, UpdateUserModel UpdateUserModel);
        Task<MiddlewareObject<bool>> logOut();
        Task<MiddlewareObject<List<GetAllAgentsUserNamesLookup>>> GetAllAgentsUserNames(GetAllUserRequest param);
    }
}
