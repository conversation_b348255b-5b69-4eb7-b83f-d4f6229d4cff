﻿using System.Text.Json.Serialization;

namespace Teletalk.Services.Models.CampaignModels
{
    public class CampaginForUpdateModel
    {
        #region Old Approach
        //public string CampaignName { get; set; }
        //public List<SubmissionFormField> SubmissionField { get; set; }
        #endregion
        #region New Approach
        [JsonPropertyName("label")]
        public string label { get; set; }

        [JsonPropertyName("fields")]
        public List<CampaignSubmissionForm> Fields { get; set; }
        #endregion
    }
}
