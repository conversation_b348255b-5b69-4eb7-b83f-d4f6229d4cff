﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Models.AgentSubmissionDTOS;
using Teletalk.Services.Models.DashboardModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DashboardController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public DashboardController(IServiceManager serviceManager)
        {
            _serviceManager = serviceManager;
        }
        [HttpPost("get-daily-target")]
        public async Task<IActionResult> GetDailyTarget(GetDailyTargetReqModel req)
        {
            var result = await _serviceManager.DashboardService.GetDaliyTarget(req);
            return Ok(result);
        }
        [HttpPost("get-monthly-target")]
        public async Task<IActionResult> GetMonthlyTarget(GetMonthlyTargetReqModel req)
        {
            var result = await _serviceManager.DashboardService.GetMonthlyTarget(req);
            return Ok(result);
        }
        [HttpPost("get-statistics-cards")]
        public async Task<IActionResult> GetStatisticsCards(GetPercentageOfAgentRankReqModel req)
        {
            var asd =  await _serviceManager.DashboardService.GetPercentageOfRank(req);
            return Ok(asd);
        }
        //[HttpPost("get-statistics-cards-v2")]
        //public async Task<IActionResult> GetStatisticsCards()
        //{
        //    var result = await _serviceManager.DashboardService.GetStatisticsCards();
        //    return Ok(result);
        //}
    }
}
