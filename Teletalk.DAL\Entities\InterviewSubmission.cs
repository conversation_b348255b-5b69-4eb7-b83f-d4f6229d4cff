﻿namespace Teletalk.DAL.Entities
{
    public class InterviewSubmission
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string NationalId { get; set; }
        public string Mobile { get; set; }
        public string PresentAddress { get; set; }
        public string MaritalStatus { get; set; }
        public string MilitaryStatus { get; set; }
        public DateTime Date { get; set; }
        public DateTime BrithOfDate { get; set; }
        public string ReferredToUs { get; set; }
        public string UniversityName { get; set; }
        public string Major { get; set; }
        public string Minor { get; set; }
        public string Degree { get; set; }
        public string EnglishFluency { get; set; }
        public string PostGrad { get; set; }
        public string WorkExperience { get; set; } // List<WorkExperience> deserialization - seralization
        public string AnyRecommendation { get; set; } // List<Recommendation> deserialization - seralization
        public int Appearance { get; set; }
        public int Personality { get; set; }
        public int SelfConfidence { get; set; }
        public int InterActionSkills { get; set; }
        public int EnglishLanguage { get; set; }
        public int Reading { get; set; }
        public int SellingSkills { get; set; }
        public int Tone { get; set; }
        public string Comment { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }
        public bool IsDelete { get; set; }
        public bool? Status { get; set; }
    }
}
