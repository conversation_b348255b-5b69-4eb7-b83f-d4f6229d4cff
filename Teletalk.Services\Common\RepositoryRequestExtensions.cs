﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Entities;
using Teletalk.Services.Models.AttendanceModels;
using Teletalk.Services.Models.AuthDTOS;

namespace Teletalk.Services.Common
{
    public static class RepositoryRequestExtensions
    {
        public static IQueryable<ApplicationUsers> SearchForUsers(this IQueryable<ApplicationUsers> users, GetAllUserRequest userRequest)
        {
            if (userRequest.UserName != null)
                users = users.Where(u => u.UserName.Contains(userRequest.UserName));

            return users;
        }
        public static IQueryable<Attendance> SearchForAttendanceUserName(this IQueryable<Attendance> attendances, GetAllAttenanceRequest attendanceRequest)
        {
            if (attendanceRequest.UserName != null)
                attendances = attendances.Where(u => u.EmployeeName.Contains(attendanceRequest.UserName));

            return attendances;
        }
    }
}
