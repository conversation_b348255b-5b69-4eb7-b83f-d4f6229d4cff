﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teletalk.DAL.Migrations
{
    public partial class RemoveUserIdFromPayslipEntity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payslips_AspNetUsers_UserId1",
                table: "Payslips");

            migrationBuilder.DropIndex(
                name: "IX_Payslips_UserId1",
                table: "Payslips");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "Payslips");

            migrationBuilder.DropColumn(
                name: "UserId1",
                table: "Payslips");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                table: "Payslips",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<string>(
                name: "UserId1",
                table: "Payslips",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Payslips_UserId1",
                table: "Payslips",
                column: "UserId1");

            migrationBuilder.AddForeignKey(
                name: "FK_Payslips_AspNetUsers_UserId1",
                table: "Payslips",
                column: "UserId1",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }
    }
}
