﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Context;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.GenreicRepo;

namespace Teletalk.DAL.Infrastructure.Repos.AgentSubmissonRepo
{
    public class AgentSubmissionRepository : RepositoryBase<AgentSubmission>, IAgentSubmissionRepository
    {
        public AgentSubmissionRepository(ApplicationDbContext context)
            : base(context)
        {
        }
    }
}
