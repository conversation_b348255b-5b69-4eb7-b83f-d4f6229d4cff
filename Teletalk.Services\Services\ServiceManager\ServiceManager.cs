﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Services.AgentSubmissionService;
using Teletalk.Services.Services.AttendanceServices;
using Teletalk.Services.Services.CampaignServices;
using Teletalk.Services.Services.DashboardService;
using Teletalk.Services.Services.InterviewSubmissionService;
using Teletalk.Services.Services.ItStockServices.LaptopsServices;
using Teletalk.Services.Services.ItStockServices.MediaServices;
using Teletalk.Services.Services.ItStockServices.NetworkDevicesServices;
using Teletalk.Services.Services.ItStockServices.ServersServices;
using Teletalk.Services.Services.ItStockServices.SoftwareServices;
using Teletalk.Services.Services.PayslipService;
using Teletalk.Services.Services.QualitySheetServices;
using Teletalk.Services.Services.RankService;
using Teletalk.Services.Services.TeamCampaignService;
using Teletalk.Services.Services.TeamLeaderServices;


namespace Teletalk.Services.Services.ServiceManager
{
    public class ServiceManager : IServiceManager
    {
        private readonly Lazy<IAgentSubmissionServices> _agentSubmissionService;
        private readonly Lazy<ICampaignService> _CampaignService;
        private readonly Lazy<ILaptopsService> _laptopsService;
        private readonly Lazy<IMediaService> _mediaService;
        private readonly Lazy<INetworkDevicesService> _networkDevicesService;
        private readonly Lazy<IServersService> _serversService;
        private readonly Lazy<ISoftwareService> _softwareService;
        private readonly Lazy<IAttendanceServices> _attendanceServices;
        private readonly Lazy<IInterviewService> _interviewService;
        private readonly Lazy<ITeamCampaignServices> _teamCampaignServices;
        private readonly Lazy<ITeamLeaderService> _teamLeaderService;
        private readonly Lazy<IQualitySheetService> _qualitySheetService;
        private readonly Lazy<IPayslipService> _payslipService;
        private readonly Lazy<IRankService> _rankService;
        private readonly Lazy<IDashboardService> _dashboardService; 
        public ServiceManager(IRepositoryManager repositoryManager, IMapper mapper, UserManager<ApplicationUsers> userManager, IHttpContextAccessor _httpContextAccessor)
        {
            _agentSubmissionService = new Lazy<IAgentSubmissionServices>(() => new AgentSubmissionServices(repositoryManager, mapper, userManager, _httpContextAccessor));
            _CampaignService = new Lazy<ICampaignService>(() => new CampaignService(repositoryManager, mapper));
            _laptopsService = new Lazy<ILaptopsService>(() => new LaptopsService(repositoryManager, mapper));
            _mediaService = new Lazy<IMediaService>(() => new MediaService(repositoryManager, mapper));
            _networkDevicesService = new Lazy<INetworkDevicesService>(() => new NetworkDevicesService(repositoryManager, mapper));
            _serversService = new Lazy<IServersService>(() => new ServersService(repositoryManager, mapper));
            _attendanceServices = new Lazy<IAttendanceServices>(() => new AttendanceService(repositoryManager, mapper, userManager));
            _softwareService = new Lazy<ISoftwareService>(() => new SoftwareService(repositoryManager, mapper));
            _interviewService = new Lazy<IInterviewService>(() => new InterviewService(repositoryManager, mapper));
            _teamCampaignServices = new Lazy<ITeamCampaignServices>(() => new TeamCampaignServices(repositoryManager, mapper));
            _teamLeaderService = new Lazy<ITeamLeaderService>(() => new TeamLeaderService(repositoryManager, mapper, _httpContextAccessor));
            _qualitySheetService = new Lazy<IQualitySheetService>(() => new QualitySheetService(repositoryManager, mapper, userManager, _httpContextAccessor));
            _payslipService = new Lazy<IPayslipService>(() => new PayslipService.PayslipService(repositoryManager, mapper, userManager));
            _rankService = new Lazy<IRankService>(() => new RankService.RankService(repositoryManager, mapper, userManager));
            _dashboardService = new Lazy<IDashboardService>(() => new DashboardService.DashboardService(repositoryManager, mapper, userManager, _httpContextAccessor));
        }
        public IAgentSubmissionServices AgentSubmissionService => _agentSubmissionService.Value;
        public ICampaignService CampaignService => _CampaignService.Value;
        public ILaptopsService LaptopsService => _laptopsService.Value;
        public IMediaService MediaService => _mediaService.Value;
        public INetworkDevicesService NetworkDevicesService => _networkDevicesService.Value;
        public IServersService ServersService => _serversService.Value;
        public ISoftwareService SoftwareService => _softwareService.Value;
        public IInterviewService InterviewService => _interviewService.Value;
        public IAttendanceServices AttendanceService => _attendanceServices.Value;
        public ITeamCampaignServices TeamCampaignServices => _teamCampaignServices.Value;
        public ITeamLeaderService TeamLeaderService => _teamLeaderService.Value;
        public IQualitySheetService QualitySheetService => _qualitySheetService.Value;
        public IPayslipService PayslipService => _payslipService.Value;
        public IRankService RankService => _rankService.Value;
        public IDashboardService DashboardService => _dashboardService.Value;
    }
}
