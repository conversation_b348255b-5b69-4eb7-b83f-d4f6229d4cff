﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.DashboardModels;

namespace Teletalk.Services.Services.DashboardService
{
    public interface IDashboardService
    {
        Task<MiddlewareObject<List<ChartDataPointModel>>> GetDaliyTarget(GetDailyTargetReqModel req);
        Task<MiddlewareObject<List<ChartDataPointModel>>> GetMonthlyTarget(GetMonthlyTargetReqModel req);
        Task<MiddlewareObject<GetPercentageOfAgentRankResModel>> GetPercentageOfRank(GetPercentageOfAgentRankReqModel req);
        Task<MiddlewareObject<List<GetStatisticsCardsResModel>>> GetStatisticsCards();
    }
}
