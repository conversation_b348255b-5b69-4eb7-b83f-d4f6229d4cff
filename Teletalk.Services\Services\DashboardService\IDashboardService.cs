﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.DashboardModels;

namespace Teletalk.Services.Services.DashboardService
{
    public interface IDashboardService
    {
        Task<MiddlewareObject<List<ChartDataPointModel>>> GetDaliyTarget(GetDailyTargetReqModel req);
        Task<MiddlewareObject<List<ChartDataPointModel>>> GetMonthlyTarget(GetMonthlyTargetReqModel req);
        Task<MiddlewareObject<GetPercentageOfAgentRankResModel>> GetPercentageOfRank(GetPercentageOfAgentRankReqModel req);
        Task<MiddlewareObject<List<GetStatisticsCardsResModel>>> GetStatisticsCards();

        // New methods for updated dashboard
        Task<MiddlewareObject<DashboardOverviewModel>> GetDashboardOverview(GetDashboardOverviewReqModel req);
        Task<MiddlewareObject<List<ChartDataPointModel>>> GetDailySales<PERSON>hart(GetDashboardDailyReqModel req);
        Task<MiddlewareObject<List<ChartDataPointModel>>> GetMonthlySalesChart(GetDashboardMonthlyReqModel req);
        Task<MiddlewareObject<DashboardCardsModel>> GetDashboardCards(GetDashboardOverviewReqModel req);
    }
}
