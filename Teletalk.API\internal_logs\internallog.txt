2024-07-29 22:59:03.7334 Debug Message Template Auto Format enabled
2024-07-29 22:59:03.8928 Trace FindReachableObject<NLog.Internal.IRenderable>:
2024-07-29 22:59:03.8928 Trace Scanning ShortDateLayoutRenderer 'Layout Renderer: ${shortdate}'
2024-07-29 22:59:03.8928 Debug FileTarget FilePathLayout with FilePathKind.Relative using AppDomain.BaseDirectory: F:\ASP.NET\SD41-G01\TeletalkCommunity\Teletalk.API\bin\Debug\net6.0\
2024-07-29 22:59:03.9007 Trace FindReachableObject<NLog.Internal.IRenderable>:
2024-07-29 22:59:03.9007 Trace Scanning LongDateLayoutRenderer 'Layout Renderer: ${longdate}'
2024-07-29 22:59:03.9007 Trace FindReachableObject<NLog.Internal.IRenderable>:
2024-07-29 22:59:03.9007 Trace Scanning LevelLayoutRenderer 'Layout Renderer: ${level}'
2024-07-29 22:59:03.9007 Trace FindReachableObject<NLog.Internal.IRenderable>:
2024-07-29 22:59:03.9007 Trace Scanning MessageLayoutRenderer 'Layout Renderer: ${message}'
2024-07-29 22:59:03.9007 Debug Adding target NLog.Targets.FileTarget(Name=logfile)
2024-07-29 22:59:03.9007 Info Registered target NLog.Targets.FileTarget(Name=logfile)
2024-07-29 22:59:03.9007 Trace ParseRulesElement
2024-07-29 22:59:03.9646 Info NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.2.2526. Product version: 5.3.2+8d52d4f3fcdbd84beae3442e1fdc8125598ed81f. GlobalAssemblyCache: False
2024-07-29 22:59:03.9646 Debug Start watching file-filter 'nlog.config' in directory: F:\ASP.NET\SD41-G01\TeletalkCommunity\Teletalk.API
2024-07-29 22:59:03.9646 Debug --- NLog configuration dump ---
2024-07-29 22:59:03.9646 Debug Targets:
2024-07-29 22:59:03.9646 Debug FileTarget(Name=logfile)
2024-07-29 22:59:03.9646 Debug Rules:
2024-07-29 22:59:03.9795 Debug logNamePattern: (:All) levels: [ Debug Info Warn Error Fatal ] writeTo: [ logfile ]
2024-07-29 22:59:03.9795 Debug --- End of NLog configuration dump ---
2024-07-29 22:59:03.9795 Trace FindReachableObject<System.Object>:
2024-07-29 22:59:03.9795 Trace Scanning LoggingRule 'logNamePattern: (:All) levels: [ Debug Info Warn Error Fatal ] writeTo: [ logfile ]'
2024-07-29 22:59:03.9795 Trace  Scanning Property Targets 'System.Collections.Generic.List`1[NLog.Targets.Target]' System.Collections.Generic.IList`1[NLog.Targets.Target]
2024-07-29 22:59:03.9795 Trace  Scanning FileTarget 'FileTarget(Name=logfile)'
2024-07-29 22:59:03.9795 Trace   Scanning Property FileName '.\logs\${shortdate}_logfile.txt' NLog.Layouts.Layout
2024-07-29 22:59:03.9795 Trace   Scanning SimpleLayout '.\logs\${shortdate}_logfile.txt'
2024-07-29 22:59:03.9795 Trace    Scanning Property LayoutRenderers 'NLog.LayoutRenderers.LayoutRenderer[]' System.Collections.Generic.IEnumerable`1[NLog.LayoutRenderers.LayoutRenderer]
2024-07-29 22:59:03.9795 Trace    Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:03.9795 Trace    Scanning ShortDateLayoutRenderer 'Layout Renderer: ${shortdate}'
2024-07-29 22:59:03.9795 Trace    Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:03.9795 Trace   Scanning Property Layout '${longdate} ${level:uppercase=true} ${message}' NLog.Layouts.Layout
2024-07-29 22:59:03.9945 Trace   Scanning SimpleLayout '${longdate} ${level:uppercase=true} ${message}'
2024-07-29 22:59:03.9945 Trace    Scanning Property LayoutRenderers 'NLog.LayoutRenderers.LayoutRenderer[]' System.Collections.Generic.IEnumerable`1[NLog.LayoutRenderers.LayoutRenderer]
2024-07-29 22:59:03.9945 Trace    Scanning LongDateLayoutRenderer 'Layout Renderer: ${longdate}'
2024-07-29 22:59:03.9945 Trace    Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:03.9945 Trace    Scanning LevelLayoutRenderer 'Layout Renderer: ${level}'
2024-07-29 22:59:03.9945 Trace    Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:03.9945 Trace    Scanning MessageLayoutRenderer 'Layout Renderer: ${message}'
2024-07-29 22:59:03.9945 Trace  Scanning Property ChildRules 'System.Collections.Generic.List`1[NLog.Config.LoggingRule]' System.Collections.Generic.IList`1[NLog.Config.LoggingRule]
2024-07-29 22:59:03.9945 Trace  Scanning Property Filters 'System.Collections.Generic.List`1[NLog.Filters.Filter]' System.Collections.Generic.IList`1[NLog.Filters.Filter]
2024-07-29 22:59:03.9945 Info Validating config: TargetNames=logfile, ConfigItems=12, FilePath=F:\ASP.NET\SD41-G01\TeletalkCommunity\Teletalk.API\nlog.config
2024-07-29 22:59:04.0149 Debug Unused target checking is started... Rule Count: 1, Target Count: 1
2024-07-29 22:59:04.0149 Debug Unused target checking is completed. Total Rule Count: 1, Total Target Count: 1, Unused Target Count: 0
2024-07-29 22:59:04.0149 Trace Initializing Layout Renderer: ${message}
2024-07-29 22:59:04.0149 Trace Initializing Layout Renderer: ${literal}
2024-07-29 22:59:04.0149 Trace Initializing Layout Renderer: ${level}
2024-07-29 22:59:04.0149 Trace Initializing Layout Renderer: ${literal}
2024-07-29 22:59:04.0149 Trace Initializing Layout Renderer: ${longdate}
2024-07-29 22:59:04.0149 Trace Initializing ${longdate} ${level:uppercase=true} ${message}
2024-07-29 22:59:04.0267 Trace FindReachableObject<NLog.Internal.IRenderable>:
2024-07-29 22:59:04.0267 Trace Scanning SimpleLayout '${longdate} ${level:uppercase=true} ${message}'
2024-07-29 22:59:04.0267 Trace  Scanning Property LayoutRenderers 'NLog.LayoutRenderers.LayoutRenderer[]' System.Collections.Generic.IEnumerable`1[NLog.LayoutRenderers.LayoutRenderer]
2024-07-29 22:59:04.0267 Trace  Scanning LongDateLayoutRenderer 'Layout Renderer: ${longdate}'
2024-07-29 22:59:04.0267 Trace  Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:04.0267 Trace  Scanning LevelLayoutRenderer 'Layout Renderer: ${level}'
2024-07-29 22:59:04.0267 Trace  Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:04.0267 Trace  Scanning MessageLayoutRenderer 'Layout Renderer: ${message}'
2024-07-29 22:59:04.0267 Trace Initializing Layout Renderer: ${literal}
2024-07-29 22:59:04.0267 Trace Initializing Layout Renderer: ${shortdate}
2024-07-29 22:59:04.0267 Trace Initializing Layout Renderer: ${literal}
2024-07-29 22:59:04.0267 Trace Initializing .\logs\${shortdate}_logfile.txt
2024-07-29 22:59:04.0267 Trace FindReachableObject<NLog.Internal.IRenderable>:
2024-07-29 22:59:04.0267 Trace Scanning SimpleLayout '.\logs\${shortdate}_logfile.txt'
2024-07-29 22:59:04.0267 Trace  Scanning Property LayoutRenderers 'NLog.LayoutRenderers.LayoutRenderer[]' System.Collections.Generic.IEnumerable`1[NLog.LayoutRenderers.LayoutRenderer]
2024-07-29 22:59:04.0267 Trace  Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:04.0267 Trace  Scanning ShortDateLayoutRenderer 'Layout Renderer: ${shortdate}'
2024-07-29 22:59:04.0267 Trace  Scanning LiteralLayoutRenderer 'Layout Renderer: ${literal}'
2024-07-29 22:59:04.0267 Trace Initializing FileTarget(Name=logfile)
2024-07-29 22:59:04.0267 Trace FindReachableObject<NLog.Layouts.Layout>:
2024-07-29 22:59:04.0267 Trace Scanning FileTarget 'FileTarget(Name=logfile)'
2024-07-29 22:59:04.0267 Trace  Scanning Property FileName '.\logs\${shortdate}_logfile.txt' NLog.Layouts.Layout
2024-07-29 22:59:04.0267 Trace  Scanning Property Layout '${longdate} ${level:uppercase=true} ${message}' NLog.Layouts.Layout
2024-07-29 22:59:04.0267 Trace FileTarget(Name=logfile) has 2 layouts
2024-07-29 22:59:04.0430 Trace FileTarget(Name=logfile): Using appenderFactory: NLog.Internal.FileAppenders.SingleProcessFileAppender+Factory
2024-07-29 22:59:04.0430 Info Configuration initialized.
