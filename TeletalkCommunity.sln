﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34511.84
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Teletalk.API", "Teletalk.API\Teletalk.API.csproj", "{436D4EAC-233D-473F-A5B0-7139F7040D93}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{ECB28BCC-127E-4936-A020-2D5F25DB64CA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business", "Business", "{295FF6F8-E304-4D06-907D-A5C1C32D7B2B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Teletalk.DAL", "Teletalk.DAL\Teletalk.DAL.csproj", "{1255B059-922B-4E3B-A5D6-43F4A60908BF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Teletalk.Services", "Teletalk.Services\Teletalk.Services.csproj", "{24A5CBE4-1647-4212-8D9C-0474799E964D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Teletalk.Models", "Teletalk.Models\Teletalk.Models.csproj", "{2FD737FA-9976-45CF-BB2A-B5252942F6C6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{16384366-2368-438A-909C-6AC09536D76D}"
	ProjectSection(SolutionItems) = preProject
		README.txt = README.txt
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Teletalk.Logger", "Teletalk.Logger\Teletalk.Logger.csproj", "{2A87DB85-DF59-4AFF-B6DA-620C01567159}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{436D4EAC-233D-473F-A5B0-7139F7040D93}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{436D4EAC-233D-473F-A5B0-7139F7040D93}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{436D4EAC-233D-473F-A5B0-7139F7040D93}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{436D4EAC-233D-473F-A5B0-7139F7040D93}.Release|Any CPU.Build.0 = Release|Any CPU
		{1255B059-922B-4E3B-A5D6-43F4A60908BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1255B059-922B-4E3B-A5D6-43F4A60908BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1255B059-922B-4E3B-A5D6-43F4A60908BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1255B059-922B-4E3B-A5D6-43F4A60908BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{24A5CBE4-1647-4212-8D9C-0474799E964D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{24A5CBE4-1647-4212-8D9C-0474799E964D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{24A5CBE4-1647-4212-8D9C-0474799E964D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{24A5CBE4-1647-4212-8D9C-0474799E964D}.Release|Any CPU.Build.0 = Release|Any CPU
		{2FD737FA-9976-45CF-BB2A-B5252942F6C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2FD737FA-9976-45CF-BB2A-B5252942F6C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2FD737FA-9976-45CF-BB2A-B5252942F6C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2FD737FA-9976-45CF-BB2A-B5252942F6C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{2A87DB85-DF59-4AFF-B6DA-620C01567159}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2A87DB85-DF59-4AFF-B6DA-620C01567159}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2A87DB85-DF59-4AFF-B6DA-620C01567159}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2A87DB85-DF59-4AFF-B6DA-620C01567159}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1255B059-922B-4E3B-A5D6-43F4A60908BF} = {ECB28BCC-127E-4936-A020-2D5F25DB64CA}
		{24A5CBE4-1647-4212-8D9C-0474799E964D} = {295FF6F8-E304-4D06-907D-A5C1C32D7B2B}
		{2FD737FA-9976-45CF-BB2A-B5252942F6C6} = {ECB28BCC-127E-4936-A020-2D5F25DB64CA}
		{2A87DB85-DF59-4AFF-B6DA-620C01567159} = {295FF6F8-E304-4D06-907D-A5C1C32D7B2B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CD439E2B-508F-4548-ABBE-914E784C86E3}
	EndGlobalSection
EndGlobal
