﻿using Microsoft.AspNetCore.Mvc;
using Teletalk.Services.Common;
using Teletalk.Services.Models.ItStockModels.ServersModels;
using Teletalk.Services.Services.ServiceManager;

namespace Teletalk.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ITStockServersController : ControllerBase
    {
        private readonly IServiceManager _serviceManager;
        public ITStockServersController(IServiceManager serviceManager) => _serviceManager = serviceManager;

        [HttpPost()]
        public async Task<MiddlewareObject<bool>> AddServer([FromBody] AddServerRequest request)
        {
            var result = await _serviceManager.ServersService.AddServer(request);
            return result;
        }
        [HttpGet()]
        public async Task<MiddlewareObject<List<ServerModel>>> GetAllServers()
        {
            var result = await _serviceManager.ServersService.GetAllServers();
            return result;
        }
        [HttpDelete()]
        public async Task<MiddlewareObject<bool>> DeleteServer(Guid id)
        {
            var result = await _serviceManager.ServersService.DeleteServer(id);
            return result;
        }
        [HttpPut()]
        public async Task<MiddlewareObject<bool>> UpdateServer(Guid id, [FromBody] ServerForUpdateModel dto)
        {
            var result = await _serviceManager.ServersService.UpdateServer(id, dto);
            return result;
        }
        // Get By Id
        [HttpGet("{id}")]
        public async Task<MiddlewareObject<ServerModel>> GetServerById(Guid id)
        {
            var result = await _serviceManager.ServersService.GetServerById(id);
            return result;
        }
    }
}
