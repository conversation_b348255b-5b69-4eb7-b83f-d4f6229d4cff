﻿using Teletalk.Services.Common;
using Teletalk.Services.Models.QualitySheetModels;

namespace Teletalk.Services.Services.QualitySheetServices
{
    public interface IQualitySheetService
    {
        Task<MiddlewareObject<List<GetAllQualitySheets>>> GetAllQualitySheets(GetQuailtySheetsByAgentId req);
        Task<MiddlewareObject<QualitySheetDetails>> GetQualitySheetDetails(Guid id);
        Task<MiddlewareObject<bool>> AddQualitySheet(AddQualitySheet qualitySheetDetails);
        Task<MiddlewareObject<bool>> UpdateQualitySheetAsync(Guid id, UpdateQualitySheetModel updateDto);
        Task<MiddlewareObject<List<QualityRatedAgentsLookup>>> GetAgentsWithQualitySheetsUsernames();
    }
}
