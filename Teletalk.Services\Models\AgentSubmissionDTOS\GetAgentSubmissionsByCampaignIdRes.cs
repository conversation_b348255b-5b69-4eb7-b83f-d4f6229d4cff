﻿using Teletalk.Models.Enums;

namespace Teletalk.Services.Models.AgentSubmissionDTOS
{
    public class GetAgentSubmissionsByCampaignIdRes
    {
        public Guid Id { get; set; }
        public Status status { get; set; } 
        public int? TeamNumber { get; set; }
        //public Guid CampaignId { get; set; }
        //public string AgentId { get; set; }
        public string AgentName { get; set; }

        // 🆕 Add an extra fields
        public Dictionary<string, string> AdditionalFields { get; set; }
    }
}
