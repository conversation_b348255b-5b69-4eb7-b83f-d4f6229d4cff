﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.Models.Enums;

namespace Teletalk.Services.Models.AgentSubmissionDTOS
{
    public class AgentSubmissionRequest
    {
        public Status status { get; set; } = Status.Pending;
        public DateTime SubmissionDate { get; set; } = DateTime.Now;
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleInitial { get; set; }
        [Phone]
        public string PhoneNumber1 { get; set; }
        [Phone]
        public string PhoneNumber2 { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string Gender { get; set; }
        public string ZipCode { get; set; }
        public string RepresentativeFirstName { get; set; }
        public string RepresentativeLastName { get; set; }
        public decimal SubscriptionPrice { get; set; }
        public decimal OneTimePrice { get; set; }
        public string PaymentMethod { get; set; }
        public string PayerName { get; set; }
        public DateTime CustomerPaymentDate { get; set; }
        public string CreditCardType { get; set; }
        public string CreditCardNumber { get; set; }
        public string CreditCardCVV { get; set; }
        public string CheckingBankName { get; set; }
        public string CheckingBankLocation { get; set; }
        public string CheckingAccountNumber { get; set; }
        public string CheckingRountingNumber { get; set; }
        public DateTime CheckingChargeDate { get; set; }
        public string EmergencyContactName { get; set; }
        public string EmergencyContactphonenumber { get; set; }
        public string AlertDeviceType { get; set; }
        public string MedicalConditions { get; set; }
        public string Comments { get; set; }
        public string SystemType { get; set; }
        public DateTime MonthlyChargingDate { get; set; }
    }
}
