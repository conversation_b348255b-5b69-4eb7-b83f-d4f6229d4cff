﻿using System.Text.Json.Serialization;

namespace Teletalk.Services.Models.CampaignModels
{
    public class AddCampaignRequest
    {
        #region Old Approach
        //public string CampaignName { get; set; }
        //public List<SubmissionFormField> SubmissionForm { get; set; } // string for test 
        #endregion

        #region New Approach
        [JsonPropertyName("label")]
        public string label { get; set; }

        [JsonPropertyName("fields")]
        public List<CampaignSubmissionForm> fields { get; set; }
        #endregion
    }
}
