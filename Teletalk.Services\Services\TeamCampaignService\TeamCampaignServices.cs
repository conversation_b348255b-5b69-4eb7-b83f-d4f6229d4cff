﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Services.Common;
using Teletalk.Services.Models.CampaignModels;
using Teletalk.Services.Models.TeamCampaignModels;

namespace Teletalk.Services.Services.TeamCampaignService
{
    public class TeamCampaignServices : ITeamCampaignServices
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        public TeamCampaignServices(IRepositoryManager repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }
        public async Task<MiddlewareObject<bool>> AddTeamCampaign(AddTeamCampaignModel request)
        {
            try
            {
                // getting the team by the request from TeamNumberCampaign 
                var teamEntity = _repository.TeamNumberCampaignRepo
                    .FindByCondition(x => x.TeamNumber == request.TeamNumber && x.CampaignId == request.CampaignId, false)
                    .FirstOrDefault();

                if (teamEntity == null)
                {
                    var newTeamNumber = _mapper.Map<TeamNumberCampaign>(request);
                    _repository.TeamNumberCampaignRepo.Create(newTeamNumber);
                    await _repository.SaveAsync();
                    return new MiddlewareObject<bool>(true, "Success");
                }
                else
                {
                    return new MiddlewareObject<bool>(error: null, message: "The Team already exists in another campaign")
                    { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
                }
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<bool>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}
