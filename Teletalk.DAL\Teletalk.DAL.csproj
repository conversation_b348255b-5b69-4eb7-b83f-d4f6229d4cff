﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.31" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.31" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.31" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.31">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Teletalk.Models\Teletalk.Models.csproj" />
  </ItemGroup>

</Project>
