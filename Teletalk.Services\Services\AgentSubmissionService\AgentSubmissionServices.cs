﻿using System.Security.Authentication;
using System.Text.Json;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Teletalk.DAL.Entities;
using Teletalk.DAL.Infrastructure.RepositoryManager;
using Teletalk.Models.Enums;
using Teletalk.Services.Common;
using Teletalk.Services.Extensions;
using Teletalk.Services.Models.AgentSubmissionDTOS;
using Teletalk.Services.Models.CampaignModels;

namespace Teletalk.Services.Services.AgentSubmissionService
{
    public class AgentSubmissionServices : IAgentSubmissionServices
    {
        private readonly IRepositoryManager _repository;
        private readonly IMapper _mapper;
        private readonly UserManager<ApplicationUsers> _UserManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AgentSubmissionServices(IRepositoryManager repository, IMapper mapper, UserManager<ApplicationUsers> userManager, IHttpContextAccessor httpContextAccessor)
        {
            _repository = repository;
            _mapper = mapper;
            _UserManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<MiddlewareObject<string>> AddAgentSubmissionService(string submissionForm, string userId, string campaignId)
        {
            try
            {
                var UserId = _httpContextAccessor.HttpContext?.User.GetUserId();

                var UserEntity = await _UserManager.Users.Where(u => u.Id == UserId && u.isActive).FirstOrDefaultAsync();

                var submission = new AgentSubmission
                {
                    Id = Guid.NewGuid(),
                    SubmissionForm = submissionForm,
                    IsDelete = false,
                    AgentId = userId,
                    CampaignId = Guid.Parse(campaignId),
                    TeamNumber = UserEntity.TeamNumber,
                    AgentName = UserEntity.UserName,
                };

                _repository.AgentSubmission.Create(submission);
                await _repository.SaveAsync();

                return new MiddlewareObject<string>("Successfully Submitted");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<string>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }


        public async Task<MiddlewareObject<CampaginForUpdateModel>> GetSubmissionFormByAgentId(string UserId)
        {
            try
            {
                var UserEntity = await _UserManager.Users.Where(u => u.Id == UserId && u.isActive).Include(x => x.Campaigns).FirstOrDefaultAsync();
                if (UserEntity != null)
                {
                    var SubmissionForm = UserEntity.Campaigns?.SubmissionForm ?? null;
                    if (SubmissionForm != null)
                    {
                        var SubmissionFormList = System.Text.Json.JsonSerializer.Deserialize<List<CampaignSubmissionForm>>(SubmissionForm);

                        var result = new CampaginForUpdateModel()
                        {
                            label = UserEntity.Campaigns.CampaignName,
                            Fields = SubmissionFormList
                        };
                        return new MiddlewareObject<CampaginForUpdateModel>(result, "Success");
                    }
                    return new MiddlewareObject<CampaginForUpdateModel>(error: null, message: "No submission form found for this user")
                    {
                        IsSuccess = false,
                        StatusCode = AppStatics.Bad_Request_Status_Code
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<CampaginForUpdateModel>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }

        #region Get All Submission Form For Team Leader
        public async Task<MiddlewareObject<List<AgentSubmissionDTO>>> GetAgentSubmissionsByUserIdAsync(string userId)
        {
            try
            {
                // Step 1: Retrieve the user by UserId
                var userEntity = await _UserManager.Users
                    .Where(u => u.Id == userId && u.isActive)
                    .Include(x => x.Campaigns)
                    .FirstOrDefaultAsync();

                // If the user is not found or the user does not have an associated campaign
                if (userEntity == null || userEntity.CampaignId == null)
                {
                    return new MiddlewareObject<List<AgentSubmissionDTO>>(error: null, message: "User not found or user does not have a campaign")
                    {
                        IsSuccess = false,
                        StatusCode = AppStatics.Bad_Request_Status_Code
                    };
                }

                var campaignId = userEntity.CampaignId.Value;

                // Step 2: Get agent submissions associated with the user's campaign
                var agentSubmissions = _repository.AgentSubmission
                    .FindByCondition(s => s.CampaignId == campaignId && !s.IsDelete, trackChanges: false)
                    .ToList();

                if (agentSubmissions == null || agentSubmissions.Count == 0)
                {
                    return new MiddlewareObject<List<AgentSubmissionDTO>>(error: null, message: "No agent submissions found for this user")
                    {
                        IsSuccess = false,
                        StatusCode = AppStatics.Bad_Request_Status_Code
                    };
                }

                // Step 3: Map agent submissions to DTOs using AutoMapper
                var agentSubmissionDTOs = _mapper.Map<List<AgentSubmissionDTO>>(agentSubmissions);

                return new MiddlewareObject<List<AgentSubmissionDTO>>(agentSubmissionDTOs, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<AgentSubmissionDTO>>(error: null, message: "Something went wrong")
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }
        }
        #endregion

        public async Task<MiddlewareObject<List<Submittedform>>> GetSubmittedFormById(string submissionId)
        {
            try
            {
                var agentSubmissions = _repository.AgentSubmission
               .FindByCondition(s => s.Id == Guid.Parse(submissionId) && !s.IsDelete, trackChanges: false)
               .ToList();

                if (agentSubmissions == null || agentSubmissions.Count == 0)
                {
                    return new MiddlewareObject<List<Submittedform>>(error: null, message: "No submissions found")
                    {
                        IsSuccess = false,
                        StatusCode = AppStatics.Bad_Request_Status_Code
                    };
                }
                var allFields = new List<Submittedform>();

                foreach (var submission in agentSubmissions)
                {
                    if (!string.IsNullOrEmpty(submission.SubmissionForm))
                    {
                        // Deserialize into a dictionary (key-value pairs)
                        var fields = JsonConvert.DeserializeObject<Dictionary<string, string>>(submission.SubmissionForm);

                        if (fields != null)
                        {
                            foreach (var field in fields)
                            {

                                allFields.Add(new Submittedform
                                {
                                    FieldName = field.Key, // Use the key (additionalProp1, additionalProp2, etc.)
                                    FieldValue = field.Value// Make sure DTO has this
                                });
                            }
                        }
                    }
                }

                return new MiddlewareObject<List<Submittedform>>(allFields, "Success");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<Submittedform>>(error: null, message: "Something went wrong")
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }
        }
        
        public async Task<MiddlewareObject<List<GetAgentsbyTeamLeaderRes>>> GetAgentsByTeamNumber(GetAgentsbyTeamLeaderReq req)
        {
            try
            {
                var agents = await _UserManager.Users
                    .Where(u => u.TeamNumber == req.TeamNumber && u.isActive && u.CampaignId == req.CampaignId)
                    .Select(x=>new GetAgentsbyTeamLeaderRes
                    {
                        UserId = x.Id,
                        UserName = x.UserName
                    })
                    .ToListAsync();

                if (agents == null || agents.Count == 0)
                {
                    return new MiddlewareObject<List<GetAgentsbyTeamLeaderRes>>(error: null, message: "No agents found for this team number")
                    {
                        IsSuccess = false,
                        StatusCode = AppStatics.Bad_Request_Status_Code
                    };
                }

                return new MiddlewareObject<List<GetAgentsbyTeamLeaderRes>>(agents, "Success");
            }
            catch (Exception)
            {
                return new MiddlewareObject<List<GetAgentsbyTeamLeaderRes>>(error: null, message: "Something went wrong")
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }
        }
        
        public async Task<MiddlewareObject<List<GetAgentSubmissionByStatusRes>>> GetAgentSubmissionByStatus(GetAgentSubmissionByStatusReq req)
        {
            try
            {
                var userId = _httpContextAccessor.HttpContext?.User.GetUserId();

                var userEntity = await _UserManager.Users.Where(u => u.Id == userId).FirstOrDefaultAsync();

                var AgentSubmissions = await _repository.AgentSubmission
                   .FindByCondition(x => x.CampaignId.Equals(userEntity.CampaignId) && x.AgentId.Equals(userEntity.Id), false)
                   .ToListAsync();

                var response = AgentSubmissions.Select(x => new GetAgentSubmissionByStatusRes 
                {
                    Name = x.AgentName,
                    Date = x.CreatedDate 
                }).ToList();

                return new MiddlewareObject<List<GetAgentSubmissionByStatusRes>>(response);
            }
            catch (Exception)
            {
                return new MiddlewareObject<List<GetAgentSubmissionByStatusRes>>(error: null, message: "Something went wrong")
                {
                    IsSuccess = false,
                    StatusCode = AppStatics.Bad_Request_Status_Code
                };
            }

            #region old
            // get the campaign of the user
            //var userEntity = await _UserManager.Users
            //    .Where(u => u.Id == req.AgentId)
            //    .FirstOrDefaultAsync();

            //if (userEntity == null)
            //    return new MiddlewareObject<GetAgentSubmissionByStatusRes>((GetAgentSubmissionByStatusRes)null, $"No User with Id {req.AgentId}");

            //var result = new GetAgentSubmissionByStatusRes();

            //if (req.Status == null) // all of the status
            //{
            //var AgentSubmissions = _repository.AgentSubmission
            //    .FindByCondition(x => x.CampaignId == userEntity.CampaignId && 
            //    x.CreatedDate <= req.ToDate &&
            //    x.CreatedDate >= req.FromDate && 
            //    req.AgentId == req.AgentId, false)
            //    .Select(x=>new GetAgentSubmissionByStatusData
            //    {
            //        CampaignId = x.CampaignId,
            //        AgentId = x.AgentId,
            //        AgentName = x.AgentName,
            //        Id = x.Id,
            //        Status = x.status.ToString(),
            //        TeamNumber = x.TeamNumber
            //    }).ToList(); 

            //var Counts = new AgentSubmissionStatusCounts
            //{
            //    TotalPendingStatus = AgentSubmissions.Count(x => x.Status == Status.Pending.ToString()),
            //    TotalApprovedStatus = AgentSubmissions.Count(x => x.Status == Status.Approved.ToString()),
            //    TotalDeletedStatus = AgentSubmissions.Count(x => x.Status == Status.Deleted.ToString()),
            //    TotalPostDateStatus = AgentSubmissions.Count(x => x.Status == Status.PostDate.ToString()),
            //};

            //result.AgentSubmissions = AgentSubmissions;
            //result.Counts = Counts;
            //}
            //else
            //{
            //var AgentSubmissions = _repository.AgentSubmission
            //   .FindByCondition(x => x.CampaignId == userEntity.CampaignId && 
            //   x.CreatedDate <= req.ToDate &&
            //   x.CreatedDate >= req.FromDate &&
            //   x.AgentId == req.AgentId &&
            //   x.status == req.Status, false)
            //   .Select(x => new GetAgentSubmissionByStatusData
            //   {
            //       CampaignId = x.CampaignId,
            //       AgentId = x.AgentId,
            //       AgentName = x.AgentName,
            //       Id = x.Id,
            //       Status = x.status.ToString(),
            //       TeamNumber = x.TeamNumber
            //   }).ToList();

            //var Counts = new AgentSubmissionStatusCounts
            //{
            //    TotalPendingStatus = AgentSubmissions.Count(x => x.Status == Status.Pending.ToString()),
            //    TotalApprovedStatus = AgentSubmissions.Count(x => x.Status == Status.Approved.ToString()),
            //    TotalDeletedStatus = AgentSubmissions.Count(x => x.Status == Status.Deleted.ToString()),
            //    TotalPostDateStatus = AgentSubmissions.Count(x => x.Status == Status.PostDate.ToString()),
            //};

            //result.AgentSubmissions = AgentSubmissions;
            //result.Counts = Counts;
            //}
            //return new MiddlewareObject<GetAgentSubmissionByStatusRes>((GetAgentSubmissionByStatusRes)result);
            #endregion
        }




        // using Agent Screen 
        public async Task<MiddlewareObject<GetAgentSubmissionGraphRes>> GetAgentSubmissionGraph(GetAgentSubmissionGraphReq req)
        {
            var userId = _httpContextAccessor.HttpContext?.User.GetUserId();
            
            var userEntity = await _UserManager.FindByIdAsync(userId);
           
            var AgentSubmissions = _repository.AgentSubmission
                .FindByCondition(x => x.CampaignId == userEntity.CampaignId && 
                x.CreatedDate <= req.ToDate &&
                x.CreatedDate >= req.FromDate, false)
               .ToList(); 

            var result = new GetAgentSubmissionGraphRes
            {
                Approved = AgentSubmissions.Count(x => x.status == Status.Pending),
                Rejected = AgentSubmissions.Count(x => x.status == Status.Approved),
                Pending = AgentSubmissions.Count(x => x.status == Status.Deleted),
                PostDate = AgentSubmissions.Count(x => x.status == Status.PostDate),
            };

            return new MiddlewareObject<GetAgentSubmissionGraphRes>(result);
        }


        public async Task<MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>> GetAgentSubmissionsByCampaignId(GetAgentSubmissionsByCampaignIdReq req)
        {
            try
            {
                var role = _httpContextAccessor.HttpContext?.User.GetUserRole();

                if (role == "Admin" && req.CampaignId != null)
                {
                    var AgentSubmissions = await _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId.Equals(req.CampaignId), false)
                        .ToListAsync();

                    if (AgentSubmissions == null || AgentSubmissions.Count == 0)
                    {
                        return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>(error: null, message: "No submissions found for this campaign")
                        {
                            IsSuccess = false,
                            StatusCode = AppStatics.Bad_Request_Status_Code
                        };
                    }

                    var response = AgentSubmissions.Select(x => new GetAgentSubmissionsByCampaignIdRes
                    {
                        Id = x.Id,
                        status = x.status,
                        TeamNumber = x.TeamNumber,
                        //CampaignId = x.CampaignId,
                        //AgentId = x.AgentId,
                        AgentName = x.AgentName,

                        AdditionalFields = string.IsNullOrWhiteSpace(x.SubmissionForm)
                            ? new Dictionary<string, string>()
                            : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(x.SubmissionForm)
                              ?? new Dictionary<string, string>()
                    }).ToList();

                    return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>(response);
                }
                else if (role == "TeamLeader")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();

                    var userEntity = await _UserManager.Users
                        .Where(u => u.Id == userId)
                        .FirstOrDefaultAsync();

                    var AgentSubmissions = await _repository.AgentSubmission
                        .FindByCondition(x => x.CampaignId.Equals(userEntity.CampaignId), false)
                        .ToListAsync();

                    if (AgentSubmissions == null || AgentSubmissions.Count == 0)
                    {
                        return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>(error: null, message: "No submissions found for this campaign")
                        {
                            IsSuccess = false,
                            StatusCode = AppStatics.Bad_Request_Status_Code
                        };
                    }

                    var response = AgentSubmissions.Select(x => new GetAgentSubmissionsByCampaignIdRes
                    {
                        Id = x.Id,
                        status = x.status,
                        TeamNumber = x.TeamNumber,
                        //CampaignId = x.CampaignId,
                        //AgentId = x.AgentId,
                        AgentName = x.AgentName,

                        AdditionalFields = string.IsNullOrWhiteSpace(x.SubmissionForm)
                            ? new Dictionary<string, string>()
                            : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(x.SubmissionForm)
                              ?? new Dictionary<string, string>()
                    }).ToList();

                    return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>(response);
                }
                else if (role == "Agent")
                {
                    var userId = _httpContextAccessor.HttpContext?.User.GetUserId();

                    var userEntity = await _UserManager.Users
                        .Where(u => u.Id == userId)
                        .FirstOrDefaultAsync();

                    var AgentSubmissions = await _repository.AgentSubmission
                       .FindByCondition(x => x.CampaignId.Equals(userEntity.CampaignId) && x.AgentId.Equals(userEntity.Id) , false)
                       .ToListAsync();

                    if (AgentSubmissions == null || AgentSubmissions.Count == 0)
                    {
                        return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>(error: null, message: "No submissions found for this campaign")
                        {
                            IsSuccess = false,
                            StatusCode = AppStatics.Bad_Request_Status_Code
                        };
                    }

                    var response = AgentSubmissions.Select(x => new GetAgentSubmissionsByCampaignIdRes
                    {
                        Id = x.Id,
                        status = x.status,
                        TeamNumber = x.TeamNumber,
                        //CampaignId = x.CampaignId,
                        //AgentId = x.AgentId,
                        AgentName = x.AgentName,

                        AdditionalFields = string.IsNullOrWhiteSpace(x.SubmissionForm)
                            ? new Dictionary<string, string>()
                            : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(x.SubmissionForm)
                              ?? new Dictionary<string, string>()
                    }).ToList();

                    return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>(response);
                }
                return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>((List<GetAgentSubmissionsByCampaignIdRes>)null, "No Data");
            }
            catch (Exception ex)
            {
                return new MiddlewareObject<List<GetAgentSubmissionsByCampaignIdRes>>(error: null, message: "something went wrong")
                { IsSuccess = false, StatusCode = AppStatics.Bad_Request_Status_Code };
            }
        }
    }
}