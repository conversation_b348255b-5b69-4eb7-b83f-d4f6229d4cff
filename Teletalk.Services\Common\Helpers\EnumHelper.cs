﻿using System.Reflection;
using System.Runtime.Serialization;

namespace Teletalk.Services.Common.Helpers
{
    public static class EnumHelper
    {
        public static List<EnumModel> GetEnumValuesWithText<T>() where T : Enum
        {
            return Enum.GetValues(typeof(T))
                       .Cast<T>()
                       .Select(e => new EnumModel
                       {
                           id = Convert.ToInt32(e),
                           name = GetEnumMemberValue(e) ?? e.ToString()
                       })
                       .ToList();
        }
        private static string? GetEnumMemberValue<T>(T enumValue) where T : Enum
        {
            return typeof(T)
                .GetMember(enumValue.ToString())
                .FirstOrDefault()?
                .GetCustomAttribute<EnumMemberAttribute>()?
                .Value;
        }
    }
}
